import 'package:flutter_test/flutter_test.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';
import 'package:imtrans/services/local_translation_service.dart';

void main() {
  group('LocalTranslationService', () {
    late LocalTranslationService translationService;

    setUp(() {
      translationService = LocalTranslationService();
    });

    tearDown(() async {
      await translationService.dispose();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        expect(translationService.isInitialized, false);
        await translationService.initialize();
        expect(translationService.isInitialized, true);
      });

      test('should throw exception when using service before initialization', () async {
        expect(
          () => translationService.translateText(
            'Hello',
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          ),
          throwsA(isA<TranslationException>()),
        );
      });
    });

    group('Language Management', () {
      setUp(() async {
        await translationService.initialize();
      });

      test('should check if language is downloaded', () async {
        // This test would require actual ML Kit setup
        // For now, we test the method exists and handles errors gracefully
        try {
          final isDownloaded = await translationService.isLanguageDownloaded(TranslateLanguage.english);
          expect(isDownloaded, isA<bool>());
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle download language request', () async {
        // This test would require actual ML Kit setup
        try {
          final success = await translationService.downloadLanguage(TranslateLanguage.english);
          expect(success, isA<bool>());
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle delete language request', () async {
        // This test would require actual ML Kit setup
        try {
          final success = await translationService.deleteLanguage(TranslateLanguage.english);
          expect(success, isA<bool>());
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should get available languages', () {
        final languages = translationService.getAvailableLanguages();
        expect(languages, isA<List<TranslateLanguage>>());
        expect(languages.isNotEmpty, true);
        expect(languages.contains(TranslateLanguage.english), true);
        expect(languages.contains(TranslateLanguage.japanese), true);
      });

      test('should get downloaded languages', () async {
        try {
          final downloadedLanguages = await translationService.getDownloadedLanguages();
          expect(downloadedLanguages, isA<List<TranslateLanguage>>());
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isNotNull);
        }
      });
    });

    group('Text Translation', () {
      setUp(() async {
        await translationService.initialize();
      });

      test('should handle empty text translation', () async {
        try {
          final result = await translationService.translateText(
            '',
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          );
          expect(result, '');
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle whitespace-only text translation', () async {
        try {
          final result = await translationService.translateText(
            '   ',
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          );
          expect(result, '   ');
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle single text translation', () async {
        try {
          final result = await translationService.translateText(
            'Hello',
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          );
          expect(result, isA<String>());
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle batch text translation', () async {
        final texts = ['Hello', 'World', '', '  ', 'Test'];
        
        try {
          final results = await translationService.translateTexts(
            texts,
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          );
          expect(results, isA<List<String>>());
          expect(results.length, texts.length);
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });

      test('should handle batch translation with empty list', () async {
        try {
          final results = await translationService.translateTexts(
            [],
            TranslateLanguage.english,
            TranslateLanguage.japanese,
          );
          expect(results, isEmpty);
        } catch (e) {
          // Expected in test environment without proper ML Kit setup
          expect(e, isA<TranslationException>());
        }
      });
    });

    group('Disposal', () {
      test('should dispose properly', () async {
        await translationService.initialize();
        expect(translationService.isInitialized, true);
        
        await translationService.dispose();
        expect(translationService.isInitialized, false);
      });
    });
  });

  group('TranslationException', () {
    test('should create with message only', () {
      const exception = TranslationException('Test error message');
      expect(exception.message, 'Test error message');
      expect(exception.originalError, null);
    });

    test('should create with message and original error', () {
      const originalError = 'Original error';
      const exception = TranslationException('Test error message', originalError);
      expect(exception.message, 'Test error message');
      expect(exception.originalError, originalError);
    });

    test('should have proper toString implementation', () {
      const exception = TranslationException('Test error message');
      final string = exception.toString();
      expect(string, contains('Test error message'));
    });

    test('should include original error in toString when present', () {
      const originalError = 'Original error';
      const exception = TranslationException('Test error message', originalError);
      final string = exception.toString();
      expect(string, contains('Test error message'));
      expect(string, contains('Original error'));
    });
  });

  group('LanguagePairs', () {
    test('should have predefined language pairs', () {
      expect(LanguagePairs.englishToJapanese.source, TranslateLanguage.english);
      expect(LanguagePairs.englishToJapanese.target, TranslateLanguage.japanese);
      
      expect(LanguagePairs.japaneseToEnglish.source, TranslateLanguage.japanese);
      expect(LanguagePairs.japaneseToEnglish.target, TranslateLanguage.english);
      
      expect(LanguagePairs.englishToChinese.source, TranslateLanguage.english);
      expect(LanguagePairs.englishToChinese.target, TranslateLanguage.chinese);
      
      expect(LanguagePairs.chineseToEnglish.source, TranslateLanguage.chinese);
      expect(LanguagePairs.chineseToEnglish.target, TranslateLanguage.english);
      
      expect(LanguagePairs.englishToKorean.source, TranslateLanguage.english);
      expect(LanguagePairs.englishToKorean.target, TranslateLanguage.korean);
      
      expect(LanguagePairs.koreanToEnglish.source, TranslateLanguage.korean);
      expect(LanguagePairs.koreanToEnglish.target, TranslateLanguage.english);
    });
  });

  group('LanguagePair', () {
    test('should create instance with source and target languages', () {
      const pair = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      expect(pair.source, TranslateLanguage.english);
      expect(pair.target, TranslateLanguage.japanese);
    });

    test('should have proper toString implementation', () {
      const pair = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      final string = pair.toString();
      expect(string, contains('en'));
      expect(string, contains('ja'));
      expect(string, contains('->'));
    });

    test('should implement equality correctly', () {
      const pair1 = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      const pair2 = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      const pair3 = LanguagePair(TranslateLanguage.japanese, TranslateLanguage.english);
      
      expect(pair1, equals(pair2));
      expect(pair1, isNot(equals(pair3)));
    });

    test('should implement hashCode correctly', () {
      const pair1 = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      const pair2 = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
      const pair3 = LanguagePair(TranslateLanguage.japanese, TranslateLanguage.english);
      
      expect(pair1.hashCode, equals(pair2.hashCode));
      expect(pair1.hashCode, isNot(equals(pair3.hashCode)));
    });
  });
}
