import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_it.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh'),
    Locale('ja'),
    Locale('de'),
    Locale('es'),
    Locale('fr'),
    Locale('it'),
    Locale('ko'),
    Locale('pt'),
    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hant')
  ];

  /// App name
  ///
  /// In en, this message translates to:
  /// **'Imtrans'**
  String get appName;

  /// App subtitle
  ///
  /// In en, this message translates to:
  /// **'Image Translator'**
  String get appSubtitle;

  /// Target language for translation
  ///
  /// In en, this message translates to:
  /// **'Target Language'**
  String get targetLanguage;

  /// Language settings title
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// App interface language setting
  ///
  /// In en, this message translates to:
  /// **'Display Language'**
  String get displayLanguage;

  /// Dark mode toggle label
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// rename button text
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading..'**
  String get loading;

  /// Error message
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Account menu item
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// Feedback menu item
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// About menu item
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Sign out button text
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// Translate button text
  ///
  /// In en, this message translates to:
  /// **'Translate'**
  String get translate;

  /// Translating status
  ///
  /// In en, this message translates to:
  /// **'Translating'**
  String get translating;

  /// Translated status
  ///
  /// In en, this message translates to:
  /// **'Translated'**
  String get translated;

  /// Failed status
  ///
  /// In en, this message translates to:
  /// **'Operation failed'**
  String get failed;

  /// Select target language title
  ///
  /// In en, this message translates to:
  /// **'Default translate to'**
  String get selectTargetLanguage;

  /// Target language selection description
  ///
  /// In en, this message translates to:
  /// **'The selected language below will be used as the default target language for translation.'**
  String get targetLanguageDescription;

  /// Dark mode setting description
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred theme mode for the app.'**
  String get darkModeDescription;

  /// Theme selection title
  ///
  /// In en, this message translates to:
  /// **'Select Theme'**
  String get selectTheme;

  /// App language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get appLanguageSetting;

  /// Contact us page title
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// App description text, used in user_drawer.dart and feedback.dart
  ///
  /// In en, this message translates to:
  /// **'High-quality Manga & Image Translation \nwith Seamless Text Overlay'**
  String get appDescription;

  /// Feedback input hint text
  ///
  /// In en, this message translates to:
  /// **'Your feedback helps us improve'**
  String get feedbackHint;

  /// Email input hint text
  ///
  /// In en, this message translates to:
  /// **'Email (optional)'**
  String get emailHint;

  /// Send button text
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// Delete data button text
  ///
  /// In en, this message translates to:
  /// **'Delete Data'**
  String get deleteData;

  /// Delete data confirmation title
  ///
  /// In en, this message translates to:
  /// **'Delete Data?'**
  String get deleteDataWarningTitle;

  /// Delete data warning message
  ///
  /// In en, this message translates to:
  /// **'Your account and all data will be deleted. This action cannot be undone.'**
  String get deleteDataWarningText;

  /// Done
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Delete data success message
  ///
  /// In en, this message translates to:
  /// **'Your data has been completely deleted.'**
  String get deleteDataSuccess;

  /// Sign in button text
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// Browser settings page title
  ///
  /// In en, this message translates to:
  /// **'Browser Settings'**
  String get browserSettings;

  /// Ad blocking setting title
  ///
  /// In en, this message translates to:
  /// **'Ad Blocking'**
  String get adBlocking;

  /// Ad blocking setting description
  ///
  /// In en, this message translates to:
  /// **'Block ads and trackers while browsing'**
  String get adBlockingDescription;

  /// Ad blocking enabled message
  ///
  /// In en, this message translates to:
  /// **'Ad blocking enabled'**
  String get adBlockingEnabled;

  /// Ad blocking disabled message
  ///
  /// In en, this message translates to:
  /// **'Ad blocking disabled'**
  String get adBlockingDisabled;

  /// URL input hint text
  ///
  /// In en, this message translates to:
  /// **'Enter URL or search keywords'**
  String get enterUrl;

  /// Error message when image fails to load, from text_overlay.dart
  ///
  /// In en, this message translates to:
  /// **'Error Loading Image'**
  String get errorLoadingImage;

  /// Message when no images are selected, from download.dart
  ///
  /// In en, this message translates to:
  /// **'Please select at least one image'**
  String get selectImages;

  /// Message when there are no valid images, from download.dart
  ///
  /// In en, this message translates to:
  /// **'No valid images to download'**
  String get noValidImages;

  /// Error message when processing images fails, from download.dart
  ///
  /// In en, this message translates to:
  /// **'Failed to process images: {error}'**
  String processingError(String error);

  /// Auto-renewal notice, from purchase_view.dart
  ///
  /// In en, this message translates to:
  /// **'Automatically renew, cancel at any time'**
  String get autoRenew;

  /// Privacy policy link text
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// User agreement link text, from purchase_view.dart
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get userAgreement;

  /// Message shown when operation takes too long, from loading.dart
  ///
  /// In en, this message translates to:
  /// **'Operation is taking longer than expected'**
  String get operationTakingLong;

  /// System theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get system;

  /// Light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// Dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get dark;

  /// chinese language name
  ///
  /// In en, this message translates to:
  /// **'Chinese'**
  String get chinese;

  /// English language name
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// Japanese language name
  ///
  /// In en, this message translates to:
  /// **'Japanese'**
  String get japanese;

  /// Korean language name
  ///
  /// In en, this message translates to:
  /// **'Korean'**
  String get korean;

  /// French language name
  ///
  /// In en, this message translates to:
  /// **'French'**
  String get french;

  /// German language name
  ///
  /// In en, this message translates to:
  /// **'German'**
  String get german;

  /// Spanish language name
  ///
  /// In en, this message translates to:
  /// **'Spanish'**
  String get spanish;

  /// Italian language name
  ///
  /// In en, this message translates to:
  /// **'Italian'**
  String get italian;

  /// Thai language name
  ///
  /// In en, this message translates to:
  /// **'Thai'**
  String get thai;

  /// Vietnamese language name
  ///
  /// In en, this message translates to:
  /// **'Vietnamese'**
  String get vietnamese;

  /// Indonesian language name
  ///
  /// In en, this message translates to:
  /// **'Indonesian'**
  String get indonesian;

  /// Malay language name
  ///
  /// In en, this message translates to:
  /// **'Malay'**
  String get malay;

  /// Feedback submission success message
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback!'**
  String get feedbackSuccess;

  /// Feedback submission error message
  ///
  /// In en, this message translates to:
  /// **'Failed to submit feedback. Please try again.'**
  String get feedbackError;

  /// Error message when feedback content is empty
  ///
  /// In en, this message translates to:
  /// **'Sorry but the content is empty'**
  String get feedbackEmpty;

  /// Error message when sending feedback fails
  ///
  /// In en, this message translates to:
  /// **'Failed to send feedback'**
  String get feedbackSendError;

  /// General error message
  ///
  /// In en, this message translates to:
  /// **'An error occurred. Please try again.'**
  String get generalError;

  /// Delete account button text
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// Delete account warning message
  ///
  /// In en, this message translates to:
  /// **'This action cannot be undone. All your data will be permanently deleted.'**
  String get deleteAccountWarning;

  /// Confirm delete button text
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// Account deletion success message
  ///
  /// In en, this message translates to:
  /// **'Account deleted successfully'**
  String get deleteSuccess;

  /// Account deletion error message
  ///
  /// In en, this message translates to:
  /// **'Failed to delete account. Please try again.'**
  String get deleteError;

  /// Subscription auto-renewal description
  ///
  /// In en, this message translates to:
  /// **'Automatically renew, cancel at any time'**
  String get subscriptionDescription;

  /// Subscribe button text
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get subscribe;

  /// Restore purchases button text
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restore;

  /// Terms of service link text
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Monthly subscription option
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Most popular subscription option label
  ///
  /// In en, this message translates to:
  /// **'Most Popular'**
  String get mostPopular;

  /// Best value subscription option label
  ///
  /// In en, this message translates to:
  /// **'Best Value'**
  String get bestValue;

  /// Unlimited manga generation feature
  ///
  /// In en, this message translates to:
  /// **'Unlimited manga generation'**
  String get unlimitedManga;

  /// High quality images feature
  ///
  /// In en, this message translates to:
  /// **'High quality images'**
  String get highQuality;

  /// Priority support feature
  ///
  /// In en, this message translates to:
  /// **'Priority support'**
  String get prioritySupport;

  /// No ads feature
  ///
  /// In en, this message translates to:
  /// **'No ads'**
  String get noAds;

  /// Popular subscription option label in purchase view
  ///
  /// In en, this message translates to:
  /// **'Popular'**
  String get popular;

  /// Free trial button text in purchase view
  ///
  /// In en, this message translates to:
  /// **'Free Trial'**
  String get freeTrial;

  /// Free trial description text in purchase view
  ///
  /// In en, this message translates to:
  /// **'Enjoy 3-day free trial, then {price} weekly'**
  String freeTrialDescription(String price);

  /// Feature description in purchase view
  ///
  /// In en, this message translates to:
  /// **'Supports File Upload For Translation'**
  String get fileUploadFeature;

  /// Feature description in purchase view
  ///
  /// In en, this message translates to:
  /// **'Higher Resolution'**
  String get higherResolutionFeature;

  /// Feature description in purchase view
  ///
  /// In en, this message translates to:
  /// **'More Accurate Translation'**
  String get accurateTranslationFeature;

  /// Feature description in purchase view
  ///
  /// In en, this message translates to:
  /// **'Unlimited Daily Image Translations'**
  String get unlimitedTranslationsFeature;

  /// Feature description in purchase view
  ///
  /// In en, this message translates to:
  /// **'Ad-Free Experience'**
  String get adFreeFeature;

  /// Account error message in purchase view
  ///
  /// In en, this message translates to:
  /// **'Account error'**
  String get accountError;

  /// No products error message in purchase view
  ///
  /// In en, this message translates to:
  /// **'No products available'**
  String get noProductsError;

  /// Processing state message
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get processing;

  /// Purchase failure message
  ///
  /// In en, this message translates to:
  /// **'Purchase failed'**
  String get purchaseFailed;

  /// Restoring purchases state message
  ///
  /// In en, this message translates to:
  /// **'Restoring purchases...'**
  String get restoring;

  /// Retry later message
  ///
  /// In en, this message translates to:
  /// **'Please try again later'**
  String get tryAgainLater;

  /// Network error message
  ///
  /// In en, this message translates to:
  /// **'Network error'**
  String get networkError;

  /// Import file button label
  ///
  /// In en, this message translates to:
  /// **'Import File'**
  String get importFile;

  /// Album button label
  ///
  /// In en, this message translates to:
  /// **'Album'**
  String get album;

  /// Camera button label
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Web image translation section title
  ///
  /// In en, this message translates to:
  /// **'Translate Web Images'**
  String get translateWebImages;

  /// Recent translations section title
  ///
  /// In en, this message translates to:
  /// **'Recent Translations'**
  String get recentTranslations;

  /// See all button text
  ///
  /// In en, this message translates to:
  /// **'See All'**
  String get seeAll;

  /// No translation history message
  ///
  /// In en, this message translates to:
  /// **'No translations yet'**
  String get noTranslationHistory;

  /// Page title for the file list, from list.dart
  ///
  /// In en, this message translates to:
  /// **'All Files'**
  String get allFiles;

  /// View mode selection title, from list.dart
  ///
  /// In en, this message translates to:
  /// **'View Mode'**
  String get viewMode;

  /// List view mode option, from list.dart
  ///
  /// In en, this message translates to:
  /// **'List Mode'**
  String get listMode;

  /// Small grid view mode option, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Small Grid Mode'**
  String get smallGridMode;

  /// Large grid view mode option, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Large Grid Mode'**
  String get largeGridMode;

  /// List mode description, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Compact list display'**
  String get listModeDescription;

  /// Small grid mode description, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Small size grid display'**
  String get smallGridModeDescription;

  /// Large grid mode description, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Large size waterfall display'**
  String get largeGridModeDescription;

  /// Single image label, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Single image'**
  String get singleImage;

  /// Multiple images label, from list.dart
  ///
  /// In en, this message translates to:
  /// **'{count} images'**
  String multipleImages(int count);

  /// Multiple images count only, from list.dart
  ///
  /// In en, this message translates to:
  /// **'{count}'**
  String multipleImagesShort(int count);

  /// Title when items are selected, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Selected {count}'**
  String selectedCount(int count);

  /// Title when items are selected with total count, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Selected {count}/{total}'**
  String selectedCountWithTotal(int count, int total);

  /// Message shown when no files are found, from list.dart
  ///
  /// In en, this message translates to:
  /// **'No files found'**
  String get noFilesFound;

  /// Download button text, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// Download status message, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Downloading..'**
  String get downloading;

  /// Success message when image is saved to gallery, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Image saved to gallery'**
  String get imageSavedToGallery;

  /// Error message when saving image fails, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Failed to save image'**
  String get failedToSaveImage;

  /// Error message when there are no images to save
  ///
  /// In en, this message translates to:
  /// **'No images to save'**
  String get noImagesToSave;

  /// Error message when image index is invalid
  ///
  /// In en, this message translates to:
  /// **'Image index out of range'**
  String get imageIndexOutOfRange;

  /// Message when saving original image because no translation exists
  ///
  /// In en, this message translates to:
  /// **'No translation result, saving original image'**
  String get noTranslationResultSavingOriginal;

  /// Error message when cannot capture image content
  ///
  /// In en, this message translates to:
  /// **'Cannot get image content'**
  String get imageContentNotFound;

  /// Error message when image data generation fails
  ///
  /// In en, this message translates to:
  /// **'Image data generation failed'**
  String get imageDataGenerationFailed;

  /// Success message when image is saved
  ///
  /// In en, this message translates to:
  /// **'Image saved successfully'**
  String get imageSavedSuccessfully;

  /// Generic saving failed message
  ///
  /// In en, this message translates to:
  /// **'Saving failed'**
  String get savingFailed;

  /// Success message when original image is saved
  ///
  /// In en, this message translates to:
  /// **'Original image saved successfully'**
  String get originalImageSavedSuccessfully;

  /// Error message when network request fails
  ///
  /// In en, this message translates to:
  /// **'Network request failed: {statusCode}'**
  String networkRequestFailed(String statusCode);

  /// Error message when cannot retrieve image data
  ///
  /// In en, this message translates to:
  /// **'Cannot get image data'**
  String get cannotGetImageData;

  /// Error message when saving original image fails
  ///
  /// In en, this message translates to:
  /// **'Failed to save original image: {error}'**
  String saveOriginalImageFailed(String error);

  /// Error message when context is missing for translated image save
  ///
  /// In en, this message translates to:
  /// **'Saving translated image requires Context'**
  String get saveTranslatedImageNeedsContext;

  /// Error message suggesting to use RepaintBoundary for translated image save
  ///
  /// In en, this message translates to:
  /// **'Saving translated images requires RepaintBoundary implementation, please use saveCurrentDisplayImage method'**
  String get saveTranslatedImageUseRepaintBoundary;

  /// Error message when saving translated image fails
  ///
  /// In en, this message translates to:
  /// **'Failed to save translated image: {error}'**
  String saveTranslatedImageFailed(String error);

  /// Success message for batch save
  ///
  /// In en, this message translates to:
  /// **'{count} images saved successfully'**
  String savedSuccessfully(int count);

  /// Partial success message for batch save
  ///
  /// In en, this message translates to:
  /// **'{successCount} images saved successfully, {failureCount} failed'**
  String savedPartially(int successCount, int failureCount);

  /// Error message when download fails, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Error downloading: {error}'**
  String errorDownloading(String error);

  /// Error message when load failed
  ///
  /// In en, this message translates to:
  /// **'Loading failed {error}'**
  String loadingFailed(String error);

  /// Delete confirmation message, from list.dart
  ///
  /// In en, this message translates to:
  /// **'Delete {count} item{s}?'**
  String deleteConfirmation(int count, String s);

  /// Drafts page title
  ///
  /// In en, this message translates to:
  /// **'Pending Tasks'**
  String get drafts;

  /// Target language selection text
  ///
  /// In en, this message translates to:
  /// **'To: {lang}'**
  String toLanguage(String lang);

  /// Translate all button text
  ///
  /// In en, this message translates to:
  /// **'Translate All'**
  String get translateAll;

  /// Message when no items are selected
  ///
  /// In en, this message translates to:
  /// **'Nothing selected'**
  String get nothingSelected;

  /// Operation failure message
  ///
  /// In en, this message translates to:
  /// **'Failed: {error}'**
  String operationFailed(String error);

  /// Message when no drafts are available
  ///
  /// In en, this message translates to:
  /// **'All Jobs Done'**
  String get allJobsDone;

  /// Message shown when image fails to load
  ///
  /// In en, this message translates to:
  /// **'Image not found'**
  String get imageNotFound;

  /// Remaining uses count message
  ///
  /// In en, this message translates to:
  /// **'{count} Uses left today'**
  String usesLeftToday(int count);

  /// Upgrade to pro message
  ///
  /// In en, this message translates to:
  /// **'Upgrade to PRO for unlimited access'**
  String get upgradeToPro;

  /// Upgrade button text
  ///
  /// In en, this message translates to:
  /// **'GET PRO'**
  String get upgradeNow;

  /// Message shown when updating translation position during scrolling
  ///
  /// In en, this message translates to:
  /// **'Updating translation position...'**
  String get updatingTranslationPosition;

  /// Message shown when no images are found on the page
  ///
  /// In en, this message translates to:
  /// **'No images found or lazy loading in progress'**
  String get noImagesFound;

  /// Message shown when unable to process images on the page
  ///
  /// In en, this message translates to:
  /// **'Unable to find any images to process on the page.'**
  String get unableToProcessImages;

  /// Error message when download fails
  ///
  /// In en, this message translates to:
  /// **'Download failed: {error}'**
  String downloadFailed(String error);

  /// Message shown when no images are selected
  ///
  /// In en, this message translates to:
  /// **'Please select at least one image'**
  String get selectAtLeastOneImage;

  /// Message shown when there are no valid images to download
  ///
  /// In en, this message translates to:
  /// **'No valid images to download'**
  String get noValidImagesToDownload;

  /// Error message when processing images fails
  ///
  /// In en, this message translates to:
  /// **'Failed to process images: {error}'**
  String failedToProcessImages(String error);

  /// Message shown when loading images
  ///
  /// In en, this message translates to:
  /// **'Loading images...'**
  String get loadingImages;

  /// Confirmation message when deleting an image in the viewer
  ///
  /// In en, this message translates to:
  /// **'Delete this item?'**
  String get deleteThisItem;

  /// Error message when image viewer fails to load
  ///
  /// In en, this message translates to:
  /// **'Error loading image viewer'**
  String get errorLoadingImageViewer;

  /// Error message when image deletion fails
  ///
  /// In en, this message translates to:
  /// **'Failed to delete image'**
  String get failedToDeleteImage;

  /// Shows when a translation was completed
  ///
  /// In en, this message translates to:
  /// **'Complete translation at \n{time}'**
  String completedTranslationAt(String time);

  /// Title shown when user reaches daily usage limit
  ///
  /// In en, this message translates to:
  /// **'Daily Limit Reached'**
  String get dailyLimitReached;

  /// Message explaining quota reset and upgrade option
  ///
  /// In en, this message translates to:
  /// **'Free quota will reset tomorrow, Watch ads for more'**
  String get quotaResetMessage;

  /// Button text for upgrading to PRO version
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get upgradeButton;

  /// Button text for trying again tomorrow
  ///
  /// In en, this message translates to:
  /// **'Try Tomorrow'**
  String get tryTomorrowButton;

  /// Follow system language setting
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get followSystem;

  /// PRO title in user drawer
  ///
  /// In en, this message translates to:
  /// **'PRO'**
  String get proTitle;

  /// Feature item: Unlock file upload
  ///
  /// In en, this message translates to:
  /// **'Unlock file translation'**
  String get unlockFileUpload;

  /// Feature item: High quality translation
  ///
  /// In en, this message translates to:
  /// **'High quality translation'**
  String get highQualityTranslation;

  /// Feature item: Ad-free experience
  ///
  /// In en, this message translates to:
  /// **'Ad-free experience'**
  String get adFreeExperience;

  /// Button text: Get PRO
  ///
  /// In en, this message translates to:
  /// **'GET PRO'**
  String get getPro;

  /// Message shown when loading products fails
  ///
  /// In en, this message translates to:
  /// **'Load Failed, please check your network connection'**
  String get loadFailed;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Message shown when purchase is successful
  ///
  /// In en, this message translates to:
  /// **'Purchase successful'**
  String get purchaseSuccessful;

  /// Message shown when purchase is restored
  ///
  /// In en, this message translates to:
  /// **'Purchase restored'**
  String get purchaseRestored;

  /// Message shown when restore fails
  ///
  /// In en, this message translates to:
  /// **'Restore failed: {error}'**
  String restoreFailed(String error);

  /// Weekly subscription option name
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get weekly;

  /// Annual subscription option name
  ///
  /// In en, this message translates to:
  /// **'Annual'**
  String get annual;

  /// Free subscription option name
  ///
  /// In en, this message translates to:
  /// **'FREE'**
  String get free;

  /// Free subscription option text
  ///
  /// In en, this message translates to:
  /// **'3 Days Free Trial'**
  String get freeText;

  /// Monthly text
  ///
  /// In en, this message translates to:
  /// **'Billed Monthly'**
  String get billedMonthly;

  /// Annual text
  ///
  /// In en, this message translates to:
  /// **'Billed Annual'**
  String get billedAnnual;

  /// weekly text
  ///
  /// In en, this message translates to:
  /// **'Billed Weekly'**
  String get billedWeekly;

  /// Free trial text
  ///
  /// In en, this message translates to:
  /// **'3 Days Free Trial'**
  String get freeTrialText;

  /// Save 30% text
  ///
  /// In en, this message translates to:
  /// **'Save 30%'**
  String get save30Percent;

  /// Login title
  ///
  /// In en, this message translates to:
  /// **'Login to your\n Account'**
  String get loginTitle;

  /// Watch ad button text
  ///
  /// In en, this message translates to:
  /// **'Get More'**
  String get watchAd;

  /// Watch ad description text
  ///
  /// In en, this message translates to:
  /// **'Watch an ad to get more usage quota'**
  String get watchAdDescription;

  /// Font settings section title
  ///
  /// In en, this message translates to:
  /// **'Font Settings'**
  String get fontSettings;

  /// Font family setting label
  ///
  /// In en, this message translates to:
  /// **'Font Family'**
  String get fontFamily;

  /// Stroke settings section title
  ///
  /// In en, this message translates to:
  /// **'Stroke Settings'**
  String get strokeSettings;

  /// Text overlay settings panel title
  ///
  /// In en, this message translates to:
  /// **'Text Style Settings'**
  String get textStyleSettings;

  /// Reset settings button tooltip
  ///
  /// In en, this message translates to:
  /// **'Reset to Defaults'**
  String get resetToDefaults;

  /// Font size setting label
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get fontSize;

  /// Font weight setting label
  ///
  /// In en, this message translates to:
  /// **'Font Weight'**
  String get fontWeight;

  /// Text color setting section title
  ///
  /// In en, this message translates to:
  /// **'Text Color'**
  String get textColor;

  /// Text color picker label
  ///
  /// In en, this message translates to:
  /// **'Text'**
  String get textLabel;

  /// Stroke color picker label
  ///
  /// In en, this message translates to:
  /// **'Stroke'**
  String get strokeLabel;

  /// Stroke width setting label
  ///
  /// In en, this message translates to:
  /// **'Stroke Width'**
  String get strokeWidth;

  /// Shadow effect setting section title
  ///
  /// In en, this message translates to:
  /// **'Shadow Effect'**
  String get shadowEffect;

  /// Shadow opacity setting label
  ///
  /// In en, this message translates to:
  /// **'Opacity'**
  String get opacity;

  /// Shadow horizontal offset setting label
  ///
  /// In en, this message translates to:
  /// **'Horizontal Offset'**
  String get horizontalOffset;

  /// Shadow vertical offset setting label
  ///
  /// In en, this message translates to:
  /// **'Vertical Offset'**
  String get verticalOffset;

  /// Shadow blur radius setting label
  ///
  /// In en, this message translates to:
  /// **'Blur Radius'**
  String get blurRadius;

  /// Font weight option: thin
  ///
  /// In en, this message translates to:
  /// **'Thin'**
  String get fontWeightThin;

  /// Font weight option: normal
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get fontWeightNormal;

  /// Font weight option: medium
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get fontWeightMedium;

  /// Font weight option: semi bold
  ///
  /// In en, this message translates to:
  /// **'Semi Bold'**
  String get fontWeightSemiBold;

  /// Font weight option: bold
  ///
  /// In en, this message translates to:
  /// **'Bold'**
  String get fontWeightBold;

  /// Font weight option: extra bold
  ///
  /// In en, this message translates to:
  /// **'Extra Bold'**
  String get fontWeightExtraBold;

  /// Font weight option: black
  ///
  /// In en, this message translates to:
  /// **'Black'**
  String get fontWeightBlack;

  /// Header for recommended websites section
  ///
  /// In en, this message translates to:
  /// **'Recommended Websites'**
  String get recommendedWebsites;

  /// Header for bookmarks section
  ///
  /// In en, this message translates to:
  /// **'Bookmarks'**
  String get myBookmarks;

  /// Empty state message when no bookmarks exist
  ///
  /// In en, this message translates to:
  /// **'No bookmarks yet'**
  String get noBookmarks;

  /// Success message when bookmark is added
  ///
  /// In en, this message translates to:
  /// **'Bookmark added'**
  String get bookmarkAdded;

  /// Message when trying to bookmark an already bookmarked page
  ///
  /// In en, this message translates to:
  /// **'Already bookmarked'**
  String get alreadyBookmarked;

  /// Error message when trying to bookmark an empty page
  ///
  /// In en, this message translates to:
  /// **'Cannot bookmark empty page'**
  String get cannotBookmarkEmptyPage;

  /// Error message when bookmark operation fails
  ///
  /// In en, this message translates to:
  /// **'Failed to add bookmark'**
  String get bookmarkFailed;

  /// Title for delete bookmark dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Bookmark'**
  String get deleteBookmark;

  /// Confirmation message for deleting a bookmark
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete bookmark \"{title}\"?'**
  String deleteBookmarkConfirm(String title);

  /// Success message when bookmark is deleted
  ///
  /// In en, this message translates to:
  /// **'Bookmark deleted'**
  String get bookmarkDeleted;

  /// Button text to manage all bookmarks
  ///
  /// In en, this message translates to:
  /// **'Manage'**
  String get manageBookmarks;

  /// Title for the full bookmarks management page
  ///
  /// In en, this message translates to:
  /// **'All Bookmarks'**
  String get allBookmarks;

  /// Header for browsing history section
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get browsingHistory;

  /// Empty state message when no browsing history exists
  ///
  /// In en, this message translates to:
  /// **'No browsing history yet'**
  String get noBrowsingHistory;

  /// Title for delete history dialog
  ///
  /// In en, this message translates to:
  /// **'Delete History'**
  String get deleteHistory;

  /// Confirmation message for deleting a history item
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this history item?'**
  String get deleteHistoryConfirm;

  /// Success message when history item is deleted
  ///
  /// In en, this message translates to:
  /// **'History item deleted'**
  String get historyDeleted;

  /// Timestamp for very recent history items
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// Timestamp for history items from minutes ago
  ///
  /// In en, this message translates to:
  /// **'{count} minutes ago'**
  String minutesAgo(int count);

  /// Timestamp for history items from hours ago
  ///
  /// In en, this message translates to:
  /// **'{count} hours ago'**
  String hoursAgo(int count);

  /// Timestamp for history items from days ago
  ///
  /// In en, this message translates to:
  /// **'{count} days ago'**
  String daysAgo(int count);
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
        'de',
        'en',
        'es',
        'fr',
        'it',
        'ja',
        'ko',
        'pt',
        'zh'
      ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+script codes are specified.
  switch (locale.languageCode) {
    case 'zh':
      {
        switch (locale.scriptCode) {
          case 'Hant':
            return AppLocalizationsZhHant();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'it':
      return AppLocalizationsIt();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'pt':
      return AppLocalizationsPt();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
