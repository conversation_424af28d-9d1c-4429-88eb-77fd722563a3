// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => '画像翻訳ツール';

  @override
  String get targetLanguage => '対象言語';

  @override
  String get language => '言語設定';

  @override
  String get displayLanguage => '表示言語';

  @override
  String get darkMode => 'ダークモード';

  @override
  String get cancel => 'キャンセル';

  @override
  String get confirm => '確認';

  @override
  String get ok => 'OK';

  @override
  String get delete => '削除';

  @override
  String get rename => '名前変更';

  @override
  String get loading => '読み込み中...';

  @override
  String get error => 'エラー';

  @override
  String get success => '成功';

  @override
  String get retry => '再試行';

  @override
  String get account => 'アカウント';

  @override
  String get feedback => 'フィードバック';

  @override
  String get about => 'アプリについて';

  @override
  String get signOut => 'サインアウト';

  @override
  String get translate => '翻訳';

  @override
  String get translating => '翻訳中';

  @override
  String get translated => '翻訳済み';

  @override
  String get failed => '操作に失敗しました';

  @override
  String get selectTargetLanguage => '対象言語を選択';

  @override
  String get targetLanguageDescription =>
      'ウェブサイトの画像を翻訳する際、以下で選択した言語が対象言語として使用されます。';

  @override
  String get darkModeDescription => 'アプリの表示モードを選択してください。';

  @override
  String get selectTheme => 'テーマを選択';

  @override
  String get appLanguageSetting => '言語';

  @override
  String get contactUs => 'お問い合わせ';

  @override
  String get appDescription => '高品質な漫画・画像翻訳\nシームレスなテキストオーバーレイ';

  @override
  String get feedbackHint => 'あなたのフィードバックは改善に役立ちます';

  @override
  String get emailHint => 'メールアドレス（任意）';

  @override
  String get send => '送信';

  @override
  String get deleteData => 'データを削除';

  @override
  String get deleteDataWarningTitle => 'データを削除しますか？';

  @override
  String get deleteDataWarningText => 'アカウントとすべてのデータが削除されます。この操作は元に戻せません。';

  @override
  String get done => '完了';

  @override
  String get deleteDataSuccess => 'データが完全に削除されました。';

  @override
  String get signIn => 'ログイン';

  @override
  String get browserSettings => 'ブラウザ設定';

  @override
  String get adBlocking => '広告ブロック';

  @override
  String get adBlockingDescription => 'ブラウジング中に広告とトラッカーをブロックする';

  @override
  String get adBlockingEnabled => '広告ブロックが有効';

  @override
  String get adBlockingDisabled => '広告ブロックが無効';

  @override
  String get enterUrl => 'URLまたは検索キーワードを入力';

  @override
  String get errorLoadingImage => '画像の読み込みに失敗しました';

  @override
  String get selectImages => '少なくとも1つの画像を選択してください';

  @override
  String get noValidImages => 'ダウンロード可能な有効な画像がありません';

  @override
  String processingError(String error) {
    return '画像の処理に失敗しました：$error';
  }

  @override
  String get autoRenew => '自動更新、いつでもキャンセル可能';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get userAgreement => '利用規約';

  @override
  String get operationTakingLong => '操作に予想以上の時間がかかっています';

  @override
  String get system => 'システム';

  @override
  String get light => 'ライト';

  @override
  String get dark => 'ダーク';

  @override
  String get chinese => '中国語';

  @override
  String get english => '英語';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '韓国語';

  @override
  String get french => 'フランス語';

  @override
  String get german => 'ドイツ語';

  @override
  String get spanish => 'スペイン語';

  @override
  String get italian => 'イタリア語';

  @override
  String get thai => 'タイ語';

  @override
  String get vietnamese => 'ベトナム語';

  @override
  String get indonesian => 'インドネシア語';

  @override
  String get malay => 'マレー語';

  @override
  String get feedbackSuccess => 'フィードバックありがとうございます！';

  @override
  String get feedbackError => 'フィードバックの送信に失敗しました。もう一度お試しください。';

  @override
  String get feedbackEmpty => '申し訳ありませんが、内容が空です';

  @override
  String get feedbackSendError => 'フィードバックの送信に失敗しました';

  @override
  String get generalError => 'エラーが発生しました。もう一度お試しください。';

  @override
  String get deleteAccount => 'アカウント削除';

  @override
  String get deleteAccountWarning => 'この操作は元に戻せません。すべてのデータが完全に削除されます。';

  @override
  String get confirmDelete => '削除を確認';

  @override
  String get deleteSuccess => 'アカウントが削除されました';

  @override
  String get deleteError => 'アカウントの削除に失敗しました。もう一度お試しください。';

  @override
  String get subscriptionDescription => '自動更新、いつでもキャンセル可能';

  @override
  String get subscribe => '購読する';

  @override
  String get restore => '購入を復元';

  @override
  String get termsOfService => '利用規約';

  @override
  String get monthly => '月額';

  @override
  String get mostPopular => '人気';

  @override
  String get bestValue => 'お得';

  @override
  String get unlimitedManga => '無制限の漫画生成';

  @override
  String get highQuality => '高品質な画像';

  @override
  String get prioritySupport => '優先サポート';

  @override
  String get noAds => '広告なし';

  @override
  String get popular => '人気';

  @override
  String get freeTrial => '無料トライアル';

  @override
  String freeTrialDescription(String price) {
    return '3日間の無料トライアル、その後週$price';
  }

  @override
  String get fileUploadFeature => 'ファイルアップロード翻訳対応';

  @override
  String get higherResolutionFeature => '高解像度';

  @override
  String get accurateTranslationFeature => 'より正確な翻訳';

  @override
  String get unlimitedTranslationsFeature => '1日無制限の画像翻訳';

  @override
  String get adFreeFeature => '広告なし体験';

  @override
  String get accountError => 'アカウントエラー';

  @override
  String get noProductsError => '利用可能な商品がありません';

  @override
  String get processing => '処理中...';

  @override
  String get purchaseFailed => '購入に失敗しました';

  @override
  String get restoring => '購入を復元中...';

  @override
  String get tryAgainLater => '後でもう一度お試しください';

  @override
  String get networkError => 'ネットワークエラー';

  @override
  String get importFile => 'ファイルをインポート';

  @override
  String get album => 'アルバム';

  @override
  String get camera => 'カメラ';

  @override
  String get translateWebImages => 'ウェブ画像を翻訳';

  @override
  String get recentTranslations => '最近の翻訳';

  @override
  String get seeAll => 'すべて見る';

  @override
  String get noTranslationHistory => '翻訳履歴がありません';

  @override
  String get allFiles => 'すべてのファイル';

  @override
  String get viewMode => '表示方式';

  @override
  String get listMode => 'リスト表示';

  @override
  String get smallGridMode => '小さいグリッド表示';

  @override
  String get largeGridMode => '大きいグリッド表示';

  @override
  String get listModeDescription => 'コンパクトなリスト表示';

  @override
  String get smallGridModeDescription => '小サイズのグリッド表示';

  @override
  String get largeGridModeDescription => '大サイズのウォーターフォール表示';

  @override
  String get singleImage => '単一画像';

  @override
  String multipleImages(int count) {
    return '$count枚の画像';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count枚';
  }

  @override
  String selectedCount(int count) {
    return '$count件選択中';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total件選択中';
  }

  @override
  String get noFilesFound => 'ファイルが見つかりません';

  @override
  String get download => 'ダウンロード';

  @override
  String get downloading => 'ダウンロード中...';

  @override
  String get imageSavedToGallery => '画像がギャラリーに保存されました';

  @override
  String get failedToSaveImage => '画像の保存に失敗しました';

  @override
  String get noImagesToSave => '保存する画像がありません';

  @override
  String get imageIndexOutOfRange => '画像インデックスが範囲外です';

  @override
  String get noTranslationResultSavingOriginal => '翻訳結果がないため、元の画像を保存します';

  @override
  String get imageContentNotFound => '画像コンテンツを取得できません';

  @override
  String get imageDataGenerationFailed => '画像データの生成に失敗しました';

  @override
  String get imageSavedSuccessfully => '画像の保存に成功しました';

  @override
  String get savingFailed => '保存に失敗しました';

  @override
  String get originalImageSavedSuccessfully => '元の画像の保存に成功しました';

  @override
  String networkRequestFailed(String statusCode) {
    return 'ネットワークリクエストが失敗しました: $statusCode';
  }

  @override
  String get cannotGetImageData => '画像データを取得できません';

  @override
  String saveOriginalImageFailed(String error) {
    return '元の画像の保存に失敗しました: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext => '翻訳画像の保存にはContextが必要です';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      '翻訳画像の保存にはRepaintBoundaryの実装が必要です。saveCurrentDisplayImageメソッドを使用してください';

  @override
  String saveTranslatedImageFailed(String error) {
    return '翻訳画像の保存に失敗しました: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count枚の画像を正常に保存しました';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount枚正常に保存、$failureCount枚失敗';
  }

  @override
  String errorDownloading(String error) {
    return 'ダウンロードに失敗しました：$error';
  }

  @override
  String loadingFailed(String error) {
    return '読み込みに失敗しました：$error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '$count件を削除しますか？';
  }

  @override
  String get drafts => '未処理タスク';

  @override
  String toLanguage(String lang) {
    return '翻訳先：$lang';
  }

  @override
  String get translateAll => 'すべて翻訳';

  @override
  String get nothingSelected => '選択されていません';

  @override
  String operationFailed(String error) {
    return '操作に失敗しました：$error';
  }

  @override
  String get allJobsDone => 'すべての作業が完了しました';

  @override
  String get imageNotFound => '画像が見つかりません';

  @override
  String usesLeftToday(int count) {
    return '本日の残り$count回';
  }

  @override
  String get upgradeToPro => 'PRO版にアップグレードして無制限に利用';

  @override
  String get upgradeNow => '取得 PRO';

  @override
  String get updatingTranslationPosition => '翻訳位置を更新中...';

  @override
  String get noImagesFound => '画像が見つからないか、読み込み中です';

  @override
  String get unableToProcessImages => 'ページ上で処理可能な画像が見つかりません';

  @override
  String downloadFailed(String error) {
    return 'ダウンロードに失敗しました：$error';
  }

  @override
  String get selectAtLeastOneImage => '少なくとも1枚の画像を選択してください';

  @override
  String get noValidImagesToDownload => 'ダウンロード可能な有効な画像がありません';

  @override
  String failedToProcessImages(String error) {
    return '画像の処理に失敗しました：$error';
  }

  @override
  String get loadingImages => '画像を読み込み中...';

  @override
  String get deleteThisItem => 'このアイテムを削除しますか？';

  @override
  String get errorLoadingImageViewer => '画像ビューアの読み込みに失敗しました';

  @override
  String get failedToDeleteImage => '画像の削除に失敗しました';

  @override
  String completedTranslationAt(String time) {
    return '翻訳完了: \n$time';
  }

  @override
  String get dailyLimitReached => '1日の制限に達しました';

  @override
  String get quotaResetMessage => '無料分は明日リセット，広告で回数増やせます';

  @override
  String get upgradeButton => 'アップグレード';

  @override
  String get tryTomorrowButton => '明日試す';

  @override
  String get followSystem => 'システム設定に従う';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'ファイル翻訳を解除';

  @override
  String get highQualityTranslation => '高品質翻訳';

  @override
  String get adFreeExperience => '広告なし体験';

  @override
  String get getPro => 'PROに変更';

  @override
  String get loadFailed => '読み込みに失敗しました、ネットワーク接続を確認してください';

  @override
  String get back => '戻る';

  @override
  String get purchaseSuccessful => '購入成功';

  @override
  String get purchaseRestored => '購入が復元されました';

  @override
  String restoreFailed(String error) {
    return '復元に失敗しました：$error';
  }

  @override
  String get weekly => '週間';

  @override
  String get annual => '年間';

  @override
  String get free => '無料';

  @override
  String get freeText => '3日間無料トライアル';

  @override
  String get billedMonthly => '月額請求';

  @override
  String get billedAnnual => '年額請求';

  @override
  String get billedWeekly => '週額請求';

  @override
  String get freeTrialText => '3日間無料トライアル';

  @override
  String get save30Percent => '30%お得';

  @override
  String get loginTitle => 'アカウントに\n ログイン';

  @override
  String get watchAd => '無料で増やす';

  @override
  String get watchAdDescription => '広告を視聴して利用回数を増やす';

  @override
  String get fontSettings => 'フォント設定';

  @override
  String get fontFamily => 'フォントファミリー';

  @override
  String get strokeSettings => '縁取り設定';

  @override
  String get textStyleSettings => 'テキストスタイル設定';

  @override
  String get resetToDefaults => 'デフォルトにリセット';

  @override
  String get fontSize => 'フォントサイズ';

  @override
  String get fontWeight => 'フォントの太さ';

  @override
  String get textColor => 'テキストカラー';

  @override
  String get textLabel => 'テキスト';

  @override
  String get strokeLabel => '縁取り';

  @override
  String get strokeWidth => '縁取りの幅';

  @override
  String get shadowEffect => 'シャドウ効果';

  @override
  String get opacity => '透明度';

  @override
  String get horizontalOffset => '水平オフセット';

  @override
  String get verticalOffset => '垂直オフセット';

  @override
  String get blurRadius => 'ぼかし半径';

  @override
  String get fontWeightThin => '細字';

  @override
  String get fontWeightNormal => '標準';

  @override
  String get fontWeightMedium => '中太';

  @override
  String get fontWeightSemiBold => 'セミボールド';

  @override
  String get fontWeightBold => '太字';

  @override
  String get fontWeightExtraBold => '極太';

  @override
  String get fontWeightBlack => 'ブラック';

  @override
  String get recommendedWebsites => 'おすすめサイト';

  @override
  String get myBookmarks => 'ブックマーク';

  @override
  String get noBookmarks => 'ブックマークがありません';

  @override
  String get bookmarkAdded => 'ブックマークに追加しました';

  @override
  String get alreadyBookmarked => '既にブックマーク済みです';

  @override
  String get cannotBookmarkEmptyPage => '空白ページはブックマークできません';

  @override
  String get bookmarkFailed => 'ブックマークの追加に失敗しました';

  @override
  String get deleteBookmark => 'ブックマークを削除';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'ブックマーク \"$title\" を削除しますか？';
  }

  @override
  String get bookmarkDeleted => 'ブックマークを削除しました';

  @override
  String get manageBookmarks => '管理';

  @override
  String get allBookmarks => 'すべてのブックマーク';

  @override
  String get browsingHistory => '履歴';

  @override
  String get noBrowsingHistory => '閲覧履歴がありません';

  @override
  String get deleteHistory => '履歴を削除';

  @override
  String get deleteHistoryConfirm => 'この履歴項目を削除しますか？';

  @override
  String get historyDeleted => '履歴項目を削除しました';

  @override
  String get justNow => 'たった今';

  @override
  String minutesAgo(int count) {
    return '$count分前';
  }

  @override
  String hoursAgo(int count) {
    return '$count時間前';
  }

  @override
  String daysAgo(int count) {
    return '$count日前';
  }
}
