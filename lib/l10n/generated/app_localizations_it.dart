// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Traduttore di immagini';

  @override
  String get targetLanguage => 'Lingua di destinazione';

  @override
  String get language => 'Lingua';

  @override
  String get displayLanguage => 'Lingua di visualizzazione';

  @override
  String get darkMode => 'Modalità scura';

  @override
  String get cancel => 'Annulla';

  @override
  String get confirm => 'Conferma';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Elimina';

  @override
  String get rename => 'Rinomina';

  @override
  String get loading => 'Caricamento...';

  @override
  String get error => 'Errore';

  @override
  String get success => 'Successo';

  @override
  String get retry => 'Riprova';

  @override
  String get account => 'Account';

  @override
  String get feedback => 'Feedback';

  @override
  String get about => 'Informazioni';

  @override
  String get signOut => 'Disconnetti';

  @override
  String get translate => 'Traduci';

  @override
  String get translating => 'Traduzione in corso';

  @override
  String get translated => 'Tradotto';

  @override
  String get failed => 'Operazione fallita';

  @override
  String get selectTargetLanguage => 'Traduci per impostazione predefinita in';

  @override
  String get targetLanguageDescription =>
      'La lingua selezionata di seguito verrà utilizzata come lingua di destinazione predefinita per la traduzione.';

  @override
  String get darkModeDescription =>
      'Scegli la modalità tema preferita per l\'app.';

  @override
  String get selectTheme => 'Seleziona tema';

  @override
  String get appLanguageSetting => 'Lingua';

  @override
  String get contactUs => 'Contattaci';

  @override
  String get appDescription =>
      'Traduzione di manga e immagini di alta qualità\ncon sovrapposizione di testo senza interruzioni';

  @override
  String get feedbackHint => 'Il tuo feedback ci aiuta a migliorare';

  @override
  String get emailHint => 'Email (opzionale)';

  @override
  String get send => 'Invia';

  @override
  String get deleteData => 'Elimina dati';

  @override
  String get deleteDataWarningTitle => 'Eliminare i dati?';

  @override
  String get deleteDataWarningText =>
      'Il tuo account e tutti i dati verranno eliminati. Questa azione non può essere annullata.';

  @override
  String get done => 'Fatto';

  @override
  String get deleteDataSuccess =>
      'I tuoi dati sono stati completamente eliminati.';

  @override
  String get signIn => 'Accedi';

  @override
  String get browserSettings => 'Impostazioni del browser';

  @override
  String get adBlocking => 'Blocco pubblicità';

  @override
  String get adBlockingDescription =>
      'Blocca pubblicità e tracker durante la navigazione';

  @override
  String get adBlockingEnabled => 'Blocco pubblicità attivato';

  @override
  String get adBlockingDisabled => 'Blocco pubblicità disattivato';

  @override
  String get enterUrl => 'Inserisci URL';

  @override
  String get errorLoadingImage =>
      'Errore durante il caricamento dell\'immagine';

  @override
  String get selectImages => 'Seleziona almeno un\'immagine';

  @override
  String get noValidImages => 'Nessuna immagine valida da scaricare';

  @override
  String processingError(String error) {
    return 'Impossibile elaborare le immagini: $error';
  }

  @override
  String get autoRenew => 'Rinnovo automatico, annulla in qualsiasi momento';

  @override
  String get privacyPolicy => 'Informativa sulla privacy';

  @override
  String get userAgreement => 'Accordo per gli utenti';

  @override
  String get operationTakingLong =>
      'L\'operazione sta richiedendo più tempo del previsto';

  @override
  String get system => 'Sistema';

  @override
  String get light => 'Chiaro';

  @override
  String get dark => 'Scuro';

  @override
  String get chinese => 'Cinese';

  @override
  String get english => 'Inglese';

  @override
  String get japanese => 'Giapponese';

  @override
  String get korean => 'Coreano';

  @override
  String get french => 'Francese';

  @override
  String get german => 'Tedesco';

  @override
  String get spanish => 'Spagnolo';

  @override
  String get italian => 'Italiano';

  @override
  String get thai => 'Tailandese';

  @override
  String get vietnamese => 'Vietnamita';

  @override
  String get indonesian => 'Indonesiano';

  @override
  String get malay => 'Malese';

  @override
  String get feedbackSuccess => 'Grazie per il tuo feedback!';

  @override
  String get feedbackError => 'Invio del feedback non riuscito. Riprova.';

  @override
  String get feedbackEmpty => 'Spiacenti, ma il contenuto è vuoto';

  @override
  String get feedbackSendError => 'Impossibile inviare feedback';

  @override
  String get generalError => 'Si è verificato un errore. Per favore riprova.';

  @override
  String get deleteAccount => 'Elimina account';

  @override
  String get deleteAccountWarning =>
      'Questa azione non può essere annullata. Tutti i tuoi dati verranno eliminati permanentemente.';

  @override
  String get confirmDelete => 'Conferma eliminazione';

  @override
  String get deleteSuccess => 'Account eliminato con successo';

  @override
  String get deleteError => 'Eliminazione dell\'account non riuscita. Riprova.';

  @override
  String get subscriptionDescription =>
      'Rinnovo automatico, annulla in qualsiasi momento';

  @override
  String get subscribe => 'Abbonati';

  @override
  String get restore => 'Ripristina';

  @override
  String get termsOfService => 'Termini di servizio';

  @override
  String get monthly => 'Mensile';

  @override
  String get mostPopular => 'Più popolare';

  @override
  String get bestValue => 'Miglior valore';

  @override
  String get unlimitedManga => 'Generazione illimitata di manga';

  @override
  String get highQuality => 'Immagini di alta qualità';

  @override
  String get prioritySupport => 'Supporto prioritario';

  @override
  String get noAds => 'Nessuna pubblicità';

  @override
  String get popular => 'Popolare';

  @override
  String get freeTrial => 'Prova gratuita';

  @override
  String freeTrialDescription(String price) {
    return 'Goditi una prova gratuita di 3 giorni, poi $price settimanale';
  }

  @override
  String get fileUploadFeature =>
      'Supporto per il caricamento di file per la traduzione';

  @override
  String get higherResolutionFeature => 'Risoluzione superiore';

  @override
  String get accurateTranslationFeature => 'Traduzione più precisa';

  @override
  String get unlimitedTranslationsFeature =>
      'Traduzioni di immagini giornaliere illimitate';

  @override
  String get adFreeFeature => 'Esperienza senza pubblicità';

  @override
  String get accountError => 'Errore dell\'account';

  @override
  String get noProductsError => 'Nessun prodotto disponibile';

  @override
  String get processing => 'Elaborazione...';

  @override
  String get purchaseFailed => 'Acquisto non riuscito';

  @override
  String get restoring => 'Ripristino acquisti...';

  @override
  String get tryAgainLater => 'Riprova più tardi';

  @override
  String get networkError => 'Errore di rete';

  @override
  String get importFile => 'Importa file';

  @override
  String get album => 'Album';

  @override
  String get camera => 'Fotocamera';

  @override
  String get translateWebImages => 'Traduci immagini web';

  @override
  String get recentTranslations => 'Traduzioni recenti';

  @override
  String get seeAll => 'Vedi tutto';

  @override
  String get noTranslationHistory =>
      'Nessuna cronologia delle traduzioni ancora';

  @override
  String get allFiles => 'Tutti i file';

  @override
  String get viewMode => 'Modalità di visualizzazione';

  @override
  String get listMode => 'Modalità elenco';

  @override
  String get smallGridMode => 'Modalità griglia piccola';

  @override
  String get largeGridMode => 'Modalità griglia grande';

  @override
  String get listModeDescription => 'Visualizzazione elenco compatto';

  @override
  String get smallGridModeDescription => 'Visualizzazione griglia piccola';

  @override
  String get largeGridModeDescription => 'Visualizzazione a cascata grande';

  @override
  String get singleImage => 'Immagine singola';

  @override
  String multipleImages(int count) {
    return '$count immagini';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return '$count selezionato/i';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total selezionato/i';
  }

  @override
  String get noFilesFound => 'Nessun file trovato';

  @override
  String get download => 'Scarica';

  @override
  String get downloading => 'Download in corso...';

  @override
  String get imageSavedToGallery => 'Immagine salvata nella galleria';

  @override
  String get failedToSaveImage => 'Salvataggio dell\'immagine non riuscito';

  @override
  String get noImagesToSave => 'Nessuna immagine da salvare';

  @override
  String get imageIndexOutOfRange => 'Indice immagine fuori range';

  @override
  String get noTranslationResultSavingOriginal =>
      'Nessun risultato di traduzione, salvataggio immagine originale';

  @override
  String get imageContentNotFound =>
      'Impossibile ottenere il contenuto dell\'immagine';

  @override
  String get imageDataGenerationFailed => 'Generazione dati immagine fallita';

  @override
  String get imageSavedSuccessfully => 'Immagine salvata con successo';

  @override
  String get savingFailed => 'Salvataggio fallito';

  @override
  String get originalImageSavedSuccessfully =>
      'Immagine originale salvata con successo';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Richiesta di rete fallita: $statusCode';
  }

  @override
  String get cannotGetImageData => 'Impossibile ottenere i dati dell\'immagine';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Salvataggio immagine originale fallito: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'Il salvataggio dell\'immagine tradotta richiede Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'Il salvataggio di immagini tradotte richiede l\'implementazione RepaintBoundary, utilizzare il metodo saveCurrentDisplayImage';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Salvataggio immagine tradotta fallito: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count immagini salvate con successo';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount salvate con successo, $failureCount fallite';
  }

  @override
  String errorDownloading(String error) {
    return 'Errore durante il download: $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Caricamento non riuscito $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return 'Eliminare $count elemento/i?';
  }

  @override
  String get drafts => 'Attività in sospeso';

  @override
  String toLanguage(String lang) {
    return 'A: $lang';
  }

  @override
  String get translateAll => 'Traduci tutto';

  @override
  String get nothingSelected => 'Nessuna selezione';

  @override
  String operationFailed(String error) {
    return 'Non riuscito: $error';
  }

  @override
  String get allJobsDone => 'Tutti i lavori sono stati completati';

  @override
  String get imageNotFound => 'Immagine non trovata';

  @override
  String usesLeftToday(int count) {
    return '$count usi rimasti oggi';
  }

  @override
  String get upgradeToPro => 'Passa a PRO per accesso illimitato';

  @override
  String get upgradeNow => 'OTTENERE PRO';

  @override
  String get updatingTranslationPosition =>
      'Aggiornamento della posizione della traduzione...';

  @override
  String get noImagesFound =>
      'Nessuna immagine trovata o caricamento lazy in corso';

  @override
  String get unableToProcessImages =>
      'Impossibile trovare immagini da elaborare sulla pagina.';

  @override
  String downloadFailed(String error) {
    return 'Download non riuscito: $error';
  }

  @override
  String get selectAtLeastOneImage => 'Seleziona almeno un\'immagine';

  @override
  String get noValidImagesToDownload => 'Nessuna immagine valida da scaricare';

  @override
  String failedToProcessImages(String error) {
    return 'Impossibile elaborare le immagini: $error';
  }

  @override
  String get loadingImages => 'Caricamento immagini...';

  @override
  String get deleteThisItem => 'Eliminare questo elemento?';

  @override
  String get errorLoadingImageViewer =>
      'Errore durante il caricamento del visualizzatore di immagini';

  @override
  String get failedToDeleteImage => 'Eliminazione dell\'immagine non riuscita';

  @override
  String completedTranslationAt(String time) {
    return 'Traduzione completata il \n$time';
  }

  @override
  String get dailyLimitReached => 'Limite giornaliero raggiunto';

  @override
  String get quotaResetMessage =>
      'Limite gratis si resetta domani. Guarda annunci per averne di più';

  @override
  String get upgradeButton => 'Aggiorna';

  @override
  String get tryTomorrowButton => 'Prova domani';

  @override
  String get followSystem => 'Segui sistema';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Sblocca traduzione file';

  @override
  String get highQualityTranslation => 'Traduzione di alta qualità';

  @override
  String get adFreeExperience => 'Esperienza senza pubblicità';

  @override
  String get getPro => 'OTTIENI PRO';

  @override
  String get loadFailed =>
      'Caricamento fallito, controlla la tua connessione di rete';

  @override
  String get back => 'Indietro';

  @override
  String get purchaseSuccessful => 'Acquisto riuscito';

  @override
  String get purchaseRestored => 'Acquisto ripristinato';

  @override
  String restoreFailed(String error) {
    return 'Ripristino fallito: $error';
  }

  @override
  String get weekly => 'Settimanale';

  @override
  String get annual => 'Annuale';

  @override
  String get free => 'GRATUITO';

  @override
  String get freeText => 'Prova gratuita di 3 giorni';

  @override
  String get billedMonthly => 'Fatturazione mensile';

  @override
  String get billedAnnual => 'Fatturazione annuale';

  @override
  String get billedWeekly => 'Fatturazione settimanale';

  @override
  String get freeTrialText => 'Prova gratuita di 3 giorni';

  @override
  String get save30Percent => 'Risparmia 30%';

  @override
  String get loginTitle => 'Accedi al tuo\n account';

  @override
  String get watchAd => 'Ottieni Altro';

  @override
  String get watchAdDescription =>
      'Guarda una pubblicità per ottenere più quota di utilizzo';

  @override
  String get fontSettings => 'Impostazioni carattere';

  @override
  String get fontFamily => 'Famiglia carattere';

  @override
  String get strokeSettings => 'Impostazioni contorno';

  @override
  String get textStyleSettings => 'Impostazioni stile testo';

  @override
  String get resetToDefaults => 'Ripristina predefiniti';

  @override
  String get fontSize => 'Dimensione carattere';

  @override
  String get fontWeight => 'Spessore carattere';

  @override
  String get textColor => 'Colore del testo';

  @override
  String get textLabel => 'Testo';

  @override
  String get strokeLabel => 'Contorno';

  @override
  String get strokeWidth => 'Larghezza contorno';

  @override
  String get shadowEffect => 'Effetto ombra';

  @override
  String get opacity => 'Opacità';

  @override
  String get horizontalOffset => 'Offset orizzontale';

  @override
  String get verticalOffset => 'Offset verticale';

  @override
  String get blurRadius => 'Raggio sfocatura';

  @override
  String get fontWeightThin => 'Sottile';

  @override
  String get fontWeightNormal => 'Normale';

  @override
  String get fontWeightMedium => 'Medio';

  @override
  String get fontWeightSemiBold => 'Semi-grassetto';

  @override
  String get fontWeightBold => 'Grassetto';

  @override
  String get fontWeightExtraBold => 'Extra-grassetto';

  @override
  String get fontWeightBlack => 'Nero';

  @override
  String get recommendedWebsites => 'Siti web consigliati';

  @override
  String get myBookmarks => 'I miei segnalibri';

  @override
  String get noBookmarks => 'Nessun segnalibro';

  @override
  String get bookmarkAdded => 'Segnalibro aggiunto';

  @override
  String get alreadyBookmarked => 'Già nei segnalibri';

  @override
  String get cannotBookmarkEmptyPage =>
      'Impossibile aggiungere una pagina vuota ai segnalibri';

  @override
  String get bookmarkFailed => 'Aggiunta segnalibro fallita';

  @override
  String get deleteBookmark => 'Elimina segnalibro';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'Sei sicuro di voler eliminare il segnalibro \"$title\"?';
  }

  @override
  String get bookmarkDeleted => 'Segnalibro eliminato';

  @override
  String get manageBookmarks => 'Gestisci';

  @override
  String get allBookmarks => 'Tutti i segnalibri';

  @override
  String get browsingHistory => 'Cronologia';

  @override
  String get noBrowsingHistory => 'Nessuna cronologia di navigazione ancora';

  @override
  String get deleteHistory => 'Elimina cronologia';

  @override
  String get deleteHistoryConfirm =>
      'Sei sicuro di voler eliminare questo elemento della cronologia?';

  @override
  String get historyDeleted => 'Elemento della cronologia eliminato';

  @override
  String get justNow => 'Proprio ora';

  @override
  String minutesAgo(int count) {
    return '$count minuti fa';
  }

  @override
  String hoursAgo(int count) {
    return '$count ore fa';
  }

  @override
  String daysAgo(int count) {
    return '$count giorni fa';
  }
}
