// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Traducteur d\'images';

  @override
  String get targetLanguage => 'Langue cible';

  @override
  String get language => 'Langue';

  @override
  String get displayLanguage => 'Langue d\'affichage';

  @override
  String get darkMode => 'Mode sombre';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Supprimer';

  @override
  String get rename => 'Renommer';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get retry => 'Réessayer';

  @override
  String get account => 'Compte';

  @override
  String get feedback => 'Commentaires';

  @override
  String get about => 'À propos';

  @override
  String get signOut => 'Déconnexion';

  @override
  String get translate => 'Traduire';

  @override
  String get translating => 'Traduction en cours';

  @override
  String get translated => 'Traduit';

  @override
  String get failed => 'Échec de l\'opération';

  @override
  String get selectTargetLanguage => 'Traduire par défaut vers';

  @override
  String get targetLanguageDescription =>
      'La langue sélectionnée ci-dessous sera utilisée comme langue cible par défaut pour la traduction.';

  @override
  String get darkModeDescription =>
      'Choisissez votre mode de thème préféré pour l\'application.';

  @override
  String get selectTheme => 'Sélectionner le thème';

  @override
  String get appLanguageSetting => 'Langue';

  @override
  String get contactUs => 'Contactez-nous';

  @override
  String get appDescription =>
      'Traduction de mangas et d\'images de haute qualité\navec superposition de texte transparente';

  @override
  String get feedbackHint => 'Vos commentaires nous aident à nous améliorer';

  @override
  String get emailHint => 'E-mail (facultatif)';

  @override
  String get send => 'Envoyer';

  @override
  String get deleteData => 'Supprimer les données';

  @override
  String get deleteDataWarningTitle => 'Supprimer les données ?';

  @override
  String get deleteDataWarningText =>
      'Votre compte et toutes les données seront supprimés. Cette action est irréversible.';

  @override
  String get done => 'Terminé';

  @override
  String get deleteDataSuccess =>
      'Vos données ont été complètement supprimées.';

  @override
  String get signIn => 'Se connecter';

  @override
  String get browserSettings => 'Paramètres du navigateur';

  @override
  String get adBlocking => 'Blocage des publicités';

  @override
  String get adBlockingDescription =>
      'Bloquer les publicités et les traceurs pendant la navigation';

  @override
  String get adBlockingEnabled => 'Blocage des publicités activé';

  @override
  String get adBlockingDisabled => 'Blocage des publicités désactivé';

  @override
  String get enterUrl => 'Entrez l\'URL ou les mots-clés de recherche';

  @override
  String get errorLoadingImage => 'Erreur lors du chargement de l\'image';

  @override
  String get selectImages => 'Veuillez sélectionner au moins une image';

  @override
  String get noValidImages => 'Aucune image valide à télécharger';

  @override
  String processingError(String error) {
    return 'Échec du traitement des images : $error';
  }

  @override
  String get autoRenew => 'Renouvellement automatique, annulez à tout moment';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get userAgreement => 'Accord utilisateur';

  @override
  String get operationTakingLong =>
      'L\'opération prend plus de temps que prévu';

  @override
  String get system => 'Système';

  @override
  String get light => 'Clair';

  @override
  String get dark => 'Sombre';

  @override
  String get chinese => 'Chinois';

  @override
  String get english => 'Anglais';

  @override
  String get japanese => 'Japonais';

  @override
  String get korean => 'Coréen';

  @override
  String get french => 'Français';

  @override
  String get german => 'Allemand';

  @override
  String get spanish => 'Espagnol';

  @override
  String get italian => 'Italien';

  @override
  String get thai => 'Thaï';

  @override
  String get vietnamese => 'Vietnamien';

  @override
  String get indonesian => 'Indonésien';

  @override
  String get malay => 'Malais';

  @override
  String get feedbackSuccess => 'Merci pour vos commentaires !';

  @override
  String get feedbackError =>
      'Échec de l\'envoi des commentaires. Veuillez réessayer.';

  @override
  String get feedbackEmpty => 'Désolé, mais le contenu est vide';

  @override
  String get feedbackSendError => 'Échec de l\'envoi des commentaires';

  @override
  String get generalError => 'Une erreur s\'est produite. Veuillez réessayer.';

  @override
  String get deleteAccount => 'Supprimer le compte';

  @override
  String get deleteAccountWarning =>
      'Cette action est irréversible. Toutes vos données seront définitivement supprimées.';

  @override
  String get confirmDelete => 'Confirmer la suppression';

  @override
  String get deleteSuccess => 'Compte supprimé avec succès';

  @override
  String get deleteError =>
      'Échec de la suppression du compte. Veuillez réessayer.';

  @override
  String get subscriptionDescription =>
      'Renouvellement automatique, annulez à tout moment';

  @override
  String get subscribe => 'S\'abonner';

  @override
  String get restore => 'Restaurer';

  @override
  String get termsOfService => 'Conditions d\'utilisation';

  @override
  String get monthly => 'Mensuel';

  @override
  String get mostPopular => 'Le plus populaire';

  @override
  String get bestValue => 'Meilleur rapport qualité-prix';

  @override
  String get unlimitedManga => 'Génération illimitée de mangas';

  @override
  String get highQuality => 'Images de haute qualité';

  @override
  String get prioritySupport => 'Support prioritaire';

  @override
  String get noAds => 'Pas de publicité';

  @override
  String get popular => 'Populaire';

  @override
  String get freeTrial => 'Essai gratuit';

  @override
  String freeTrialDescription(String price) {
    return 'Profitez d\'un essai gratuit de 3 jours, puis $price par semaine';
  }

  @override
  String get fileUploadFeature =>
      'Prise en charge du téléchargement de fichiers pour la traduction';

  @override
  String get higherResolutionFeature => 'Résolution supérieure';

  @override
  String get accurateTranslationFeature => 'Traduction plus précise';

  @override
  String get unlimitedTranslationsFeature =>
      'Traductions d\'images quotidiennes illimitées';

  @override
  String get adFreeFeature => 'Expérience sans publicité';

  @override
  String get accountError => 'Erreur de compte';

  @override
  String get noProductsError => 'Aucun produit disponible';

  @override
  String get processing => 'Traitement...';

  @override
  String get purchaseFailed => 'Échec de l\'achat';

  @override
  String get restoring => 'Restauration des achats...';

  @override
  String get tryAgainLater => 'Veuillez réessayer plus tard';

  @override
  String get networkError => 'Erreur réseau';

  @override
  String get importFile => 'Importer un fichier';

  @override
  String get album => 'Album';

  @override
  String get camera => 'Appareil photo';

  @override
  String get translateWebImages => 'Traduire les images Web';

  @override
  String get recentTranslations => 'Traductions récentes';

  @override
  String get seeAll => 'Voir tout';

  @override
  String get noTranslationHistory =>
      'Aucun historique de traduction pour le moment';

  @override
  String get allFiles => 'Tous les fichiers';

  @override
  String get viewMode => 'Mode d\'affichage';

  @override
  String get listMode => 'Mode liste';

  @override
  String get smallGridMode => 'Mode petite grille';

  @override
  String get largeGridMode => 'Mode grande grille';

  @override
  String get listModeDescription => 'Affichage de liste compact';

  @override
  String get smallGridModeDescription => 'Affichage en petite grille';

  @override
  String get largeGridModeDescription => 'Affichage en cascade large';

  @override
  String get singleImage => 'Image unique';

  @override
  String multipleImages(int count) {
    return '$count images';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return '$count sélectionné(s)';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total sélectionné(s)';
  }

  @override
  String get noFilesFound => 'Aucun fichier trouvé';

  @override
  String get download => 'Télécharger';

  @override
  String get downloading => 'Téléchargement...';

  @override
  String get imageSavedToGallery => 'Image enregistrée dans la galerie';

  @override
  String get failedToSaveImage => 'Échec de l\'enregistrement de l\'image';

  @override
  String get noImagesToSave => 'Aucune image à enregistrer';

  @override
  String get imageIndexOutOfRange => 'Index d\'image hors limite';

  @override
  String get noTranslationResultSavingOriginal =>
      'Aucun résultat de traduction, enregistrement de l\'image originale';

  @override
  String get imageContentNotFound =>
      'Impossible d\'obtenir le contenu de l\'image';

  @override
  String get imageDataGenerationFailed =>
      'Échec de la génération des données d\'image';

  @override
  String get imageSavedSuccessfully => 'Image enregistrée avec succès';

  @override
  String get savingFailed => 'Échec de l\'enregistrement';

  @override
  String get originalImageSavedSuccessfully =>
      'Image originale enregistrée avec succès';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Échec de la requête réseau: $statusCode';
  }

  @override
  String get cannotGetImageData =>
      'Impossible d\'obtenir les données de l\'image';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Échec de l\'enregistrement de l\'image originale: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'L\'enregistrement d\'image traduite nécessite un Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'L\'enregistrement d\'images traduites nécessite une implémentation RepaintBoundary, utilisez la méthode saveCurrentDisplayImage';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Échec de l\'enregistrement de l\'image traduite: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count images enregistrées avec succès';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount enregistrées avec succès, $failureCount échouées';
  }

  @override
  String errorDownloading(String error) {
    return 'Erreur de téléchargement : $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Échec du chargement $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return 'Supprimer $count élément(s) ?';
  }

  @override
  String get drafts => 'Tâches en attente';

  @override
  String toLanguage(String lang) {
    return 'Vers : $lang';
  }

  @override
  String get translateAll => 'Traduire tout';

  @override
  String get nothingSelected => 'Rien de sélectionné';

  @override
  String operationFailed(String error) {
    return 'Échec : $error';
  }

  @override
  String get allJobsDone => 'Toutes les tâches sont terminées';

  @override
  String get imageNotFound => 'Image non trouvée';

  @override
  String usesLeftToday(int count) {
    return '$count Utilisations restantes aujourd\'hui';
  }

  @override
  String get upgradeToPro => 'Passez à PRO pour un accès illimité';

  @override
  String get upgradeNow => 'OBTENIR PRO';

  @override
  String get updatingTranslationPosition =>
      'Mise à jour de la position de la traduction...';

  @override
  String get noImagesFound =>
      'Aucune image trouvée ou chargement différé en cours';

  @override
  String get unableToProcessImages =>
      'Impossible de trouver des images à traiter sur la page.';

  @override
  String downloadFailed(String error) {
    return 'Échec du téléchargement : $error';
  }

  @override
  String get selectAtLeastOneImage =>
      'Veuillez sélectionner au moins une image';

  @override
  String get noValidImagesToDownload => 'Aucune image valide à télécharger';

  @override
  String failedToProcessImages(String error) {
    return 'Échec du traitement des images : $error';
  }

  @override
  String get loadingImages => 'Chargement des images...';

  @override
  String get deleteThisItem => 'Supprimer cet élément ?';

  @override
  String get errorLoadingImageViewer =>
      'Erreur lors du chargement de la visionneuse d\'images';

  @override
  String get failedToDeleteImage => 'Échec de la suppression de l\'image';

  @override
  String completedTranslationAt(String time) {
    return 'Traduction terminée le \n$time';
  }

  @override
  String get dailyLimitReached => 'Limite quotidienne atteinte';

  @override
  String get quotaResetMessage =>
      'Votre quota gratuit est réinitialisé demain. Regardez des pubs pour en avoir plus';

  @override
  String get upgradeButton => 'Mettre à niveau';

  @override
  String get tryTomorrowButton => 'Essayer demain';

  @override
  String get followSystem => 'Suivre le système';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Débloquer la traduction de fichiers';

  @override
  String get highQualityTranslation => 'Traduction haute qualité';

  @override
  String get adFreeExperience => 'Expérience sans publicité';

  @override
  String get getPro => 'OBTENIR PRO';

  @override
  String get loadFailed =>
      'Échec du chargement, veuillez vérifier votre connexion réseau';

  @override
  String get back => 'Retour';

  @override
  String get purchaseSuccessful => 'Achat réussi';

  @override
  String get purchaseRestored => 'Achat restauré';

  @override
  String restoreFailed(String error) {
    return 'Échec de la restauration : $error';
  }

  @override
  String get weekly => 'Hebdomadaire';

  @override
  String get annual => 'Annuel';

  @override
  String get free => 'GRATUIT';

  @override
  String get freeText => 'Essai gratuit de 3 jours';

  @override
  String get billedMonthly => 'Facturation mensuelle';

  @override
  String get billedAnnual => 'Facturation annuelle';

  @override
  String get billedWeekly => 'Facturation hebdomadaire';

  @override
  String get freeTrialText => 'Essai gratuit de 3 jours';

  @override
  String get save30Percent => 'Économisez 30%';

  @override
  String get loginTitle => 'Connectez-vous à\n votre compte';

  @override
  String get watchAd => '+ de Gratuit';

  @override
  String get watchAdDescription =>
      'Regardez une publicité pour obtenir plus de quotas d\'utilisation';

  @override
  String get fontSettings => 'Paramètres de police';

  @override
  String get fontFamily => 'Famille de police';

  @override
  String get strokeSettings => 'Paramètres de contour';

  @override
  String get textStyleSettings => 'Paramètres de style de texte';

  @override
  String get resetToDefaults => 'Réinitialiser aux valeurs par défaut';

  @override
  String get fontSize => 'Taille de police';

  @override
  String get fontWeight => 'Épaisseur de police';

  @override
  String get textColor => 'Couleur du texte';

  @override
  String get textLabel => 'Texte';

  @override
  String get strokeLabel => 'Contour';

  @override
  String get strokeWidth => 'Largeur du contour';

  @override
  String get shadowEffect => 'Effet d\'ombre';

  @override
  String get opacity => 'Opacité';

  @override
  String get horizontalOffset => 'Décalage horizontal';

  @override
  String get verticalOffset => 'Décalage vertical';

  @override
  String get blurRadius => 'Rayon de flou';

  @override
  String get fontWeightThin => 'Fin';

  @override
  String get fontWeightNormal => 'Normal';

  @override
  String get fontWeightMedium => 'Moyen';

  @override
  String get fontWeightSemiBold => 'Semi-gras';

  @override
  String get fontWeightBold => 'Gras';

  @override
  String get fontWeightExtraBold => 'Extra-gras';

  @override
  String get fontWeightBlack => 'Noir';

  @override
  String get recommendedWebsites => 'Sites Web recommandés';

  @override
  String get myBookmarks => 'Mes favoris';

  @override
  String get noBookmarks => 'Aucun favori';

  @override
  String get bookmarkAdded => 'Favori ajouté';

  @override
  String get alreadyBookmarked => 'Déjà en favoris';

  @override
  String get cannotBookmarkEmptyPage =>
      'Impossible d\'ajouter une page vide aux favoris';

  @override
  String get bookmarkFailed => 'Échec de l\'ajout aux favoris';

  @override
  String get deleteBookmark => 'Supprimer le favori';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'Êtes-vous sûr de vouloir supprimer le favori \"$title\" ?';
  }

  @override
  String get bookmarkDeleted => 'Favori supprimé';

  @override
  String get manageBookmarks => 'Gérer';

  @override
  String get allBookmarks => 'Tous les favoris';

  @override
  String get browsingHistory => 'Historique';

  @override
  String get noBrowsingHistory =>
      'Aucun historique de navigation pour le moment';

  @override
  String get deleteHistory => 'Supprimer l\'historique';

  @override
  String get deleteHistoryConfirm =>
      'Êtes-vous sûr de vouloir supprimer cet élément de l\'historique ?';

  @override
  String get historyDeleted => 'Élément de l\'historique supprimé';

  @override
  String get justNow => 'À l\'instant';

  @override
  String minutesAgo(int count) {
    return 'il y a $count minutes';
  }

  @override
  String hoursAgo(int count) {
    return 'il y a $count heures';
  }

  @override
  String daysAgo(int count) {
    return 'il y a $count jours';
  }
}
