// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Tradutor de Imagens';

  @override
  String get targetLanguage => 'Idioma Alvo';

  @override
  String get language => 'Idioma';

  @override
  String get displayLanguage => 'Idioma de Exibição';

  @override
  String get darkMode => 'Modo Escuro';

  @override
  String get cancel => 'Cancelar';

  @override
  String get confirm => 'Confirmar';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Excluir';

  @override
  String get rename => 'Renomear';

  @override
  String get loading => 'Carregando..';

  @override
  String get error => 'Erro';

  @override
  String get success => 'Sucesso';

  @override
  String get retry => 'Tentar Novamente';

  @override
  String get account => 'Conta';

  @override
  String get feedback => 'Feedback';

  @override
  String get about => 'Sobre';

  @override
  String get signOut => 'Sair';

  @override
  String get translate => 'Traduzir';

  @override
  String get translating => 'Traduzindo';

  @override
  String get translated => 'Traduzido';

  @override
  String get failed => 'Operação falhou';

  @override
  String get selectTargetLanguage => 'Traduzir por padrão para';

  @override
  String get targetLanguageDescription =>
      'O idioma selecionado abaixo será usado como idioma alvo padrão para tradução.';

  @override
  String get darkModeDescription =>
      'Escolha seu modo de tema preferido para o aplicativo.';

  @override
  String get selectTheme => 'Selecionar Tema';

  @override
  String get appLanguageSetting => 'Idioma';

  @override
  String get contactUs => 'Contate-nos';

  @override
  String get appDescription =>
      'Tradução de Manga e Imagens de Alta Qualidade \ncom Sobreposição de Texto Perfeita';

  @override
  String get feedbackHint => 'Seu feedback nos ajuda a melhorar';

  @override
  String get emailHint => 'Email (opcional)';

  @override
  String get send => 'Enviar';

  @override
  String get deleteData => 'Excluir Dados';

  @override
  String get deleteDataWarningTitle => 'Excluir Dados?';

  @override
  String get deleteDataWarningText =>
      'Sua conta e todos os dados serão excluídos. Esta ação não pode ser desfeita.';

  @override
  String get done => 'Concluído';

  @override
  String get deleteDataSuccess => 'Seus dados foram completamente excluídos.';

  @override
  String get signIn => 'Entrar';

  @override
  String get browserSettings => 'Configurações do navegador';

  @override
  String get adBlocking => 'Bloqueio de anúncios';

  @override
  String get adBlockingDescription =>
      'Bloquear anúncios e rastreadores durante a navegação';

  @override
  String get adBlockingEnabled => 'Bloqueio de anúncios ativado';

  @override
  String get adBlockingDisabled => 'Bloqueio de anúncios desativado';

  @override
  String get enterUrl => 'Inserir URL ou palavras-chave de pesquisa';

  @override
  String get errorLoadingImage => 'Erro ao Carregar Imagem';

  @override
  String get selectImages => 'Por favor, selecione pelo menos uma imagem';

  @override
  String get noValidImages => 'Nenhuma imagem válida para baixar';

  @override
  String processingError(String error) {
    return 'Falha ao processar imagens: $error';
  }

  @override
  String get autoRenew => 'Renovação automática, cancele a qualquer momento';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get userAgreement => 'Acordo do Usuário';

  @override
  String get operationTakingLong =>
      'A operação está demorando mais do que o esperado';

  @override
  String get system => 'Sistema';

  @override
  String get light => 'Claro';

  @override
  String get dark => 'Escuro';

  @override
  String get chinese => 'Chinês';

  @override
  String get english => 'Inglês';

  @override
  String get japanese => 'Japonês';

  @override
  String get korean => 'Coreano';

  @override
  String get french => 'Francês';

  @override
  String get german => 'Alemão';

  @override
  String get spanish => 'Espanhol';

  @override
  String get italian => 'Italiano';

  @override
  String get thai => 'Tailandês';

  @override
  String get vietnamese => 'Vietnamita';

  @override
  String get indonesian => 'Indonésio';

  @override
  String get malay => 'Malaio';

  @override
  String get feedbackSuccess => 'Obrigado pelo seu feedback!';

  @override
  String get feedbackError =>
      'Falha ao enviar feedback. Por favor, tente novamente.';

  @override
  String get feedbackEmpty => 'Desculpe, mas o conteúdo está vazio';

  @override
  String get feedbackSendError => 'Falha ao enviar feedback';

  @override
  String get generalError => 'Ocorreu um erro. Por favor, tente novamente.';

  @override
  String get deleteAccount => 'Excluir Conta';

  @override
  String get deleteAccountWarning =>
      'Esta ação não pode ser desfeita. Todos os seus dados serão permanentemente excluídos.';

  @override
  String get confirmDelete => 'Confirmar Exclusão';

  @override
  String get deleteSuccess => 'Conta excluída com sucesso';

  @override
  String get deleteError =>
      'Falha ao excluir conta. Por favor, tente novamente.';

  @override
  String get subscriptionDescription =>
      'Renovação automática, cancele a qualquer momento';

  @override
  String get subscribe => 'Assinar';

  @override
  String get restore => 'Restaurar';

  @override
  String get termsOfService => 'Termos de Serviço';

  @override
  String get monthly => 'Mensal';

  @override
  String get mostPopular => 'Mais Popular';

  @override
  String get bestValue => 'Melhor Valor';

  @override
  String get unlimitedManga => 'Geração ilimitada de manga';

  @override
  String get highQuality => 'Imagens de alta qualidade';

  @override
  String get prioritySupport => 'Suporte prioritário';

  @override
  String get noAds => 'Sem anúncios';

  @override
  String get popular => 'Popular';

  @override
  String get freeTrial => 'Teste Grátis';

  @override
  String freeTrialDescription(String price) {
    return 'Aproveite um teste gratuito de 3 dias, depois $price semanal';
  }

  @override
  String get fileUploadFeature =>
      'Suporte para upload de arquivos para tradução';

  @override
  String get higherResolutionFeature => 'Maior resolução';

  @override
  String get accurateTranslationFeature => 'Tradução mais precisa';

  @override
  String get unlimitedTranslationsFeature =>
      'Traduções diárias ilimitadas de imagens';

  @override
  String get adFreeFeature => 'Experiência sem anúncios';

  @override
  String get accountError => 'Erro na conta';

  @override
  String get noProductsError => 'Nenhum produto disponível';

  @override
  String get processing => 'Processando...';

  @override
  String get purchaseFailed => 'Falha na compra';

  @override
  String get restoring => 'Restaurando compras...';

  @override
  String get tryAgainLater => 'Por favor, tente novamente mais tarde';

  @override
  String get networkError => 'Erro de rede';

  @override
  String get importFile => 'Importar Arquivo';

  @override
  String get album => 'Álbum';

  @override
  String get camera => 'Câmera';

  @override
  String get translateWebImages => 'Traduzir Imagens da Web';

  @override
  String get recentTranslations => 'Traduções Recentes';

  @override
  String get seeAll => 'Ver Todos';

  @override
  String get noTranslationHistory => 'Ainda não há histórico de tradução';

  @override
  String get allFiles => 'Todos os Arquivos';

  @override
  String get viewMode => 'Modo de visualização';

  @override
  String get listMode => 'Modo de lista';

  @override
  String get smallGridMode => 'Modo de grade pequena';

  @override
  String get largeGridMode => 'Modo de grade grande';

  @override
  String get listModeDescription => 'Exibição de lista compacta';

  @override
  String get smallGridModeDescription => 'Exibição em grade pequena';

  @override
  String get largeGridModeDescription => 'Exibição em cascata grande';

  @override
  String get singleImage => 'Imagem única';

  @override
  String multipleImages(int count) {
    return '$count imagens';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return '$count selecionados';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total selecionados';
  }

  @override
  String get noFilesFound => 'Nenhum arquivo encontrado';

  @override
  String get download => 'Baixar';

  @override
  String get downloading => 'Baixando...';

  @override
  String get imageSavedToGallery => 'Imagem salva na galeria';

  @override
  String get failedToSaveImage => 'Falha ao salvar imagem';

  @override
  String get noImagesToSave => 'Nenhuma imagem para salvar';

  @override
  String get imageIndexOutOfRange => 'Índice da imagem fora do alcance';

  @override
  String get noTranslationResultSavingOriginal =>
      'Sem resultado de tradução, salvando imagem original';

  @override
  String get imageContentNotFound =>
      'Não é possível obter o conteúdo da imagem';

  @override
  String get imageDataGenerationFailed => 'Falha na geração de dados da imagem';

  @override
  String get imageSavedSuccessfully => 'Imagem salva com sucesso';

  @override
  String get savingFailed => 'Falha ao salvar';

  @override
  String get originalImageSavedSuccessfully =>
      'Imagem original salva com sucesso';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Falha na solicitação de rede: $statusCode';
  }

  @override
  String get cannotGetImageData => 'Não é possível obter dados da imagem';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Falha ao salvar imagem original: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'Salvar imagem traduzida requer Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'Salvar imagens traduzidas requer implementação RepaintBoundary, use o método saveCurrentDisplayImage';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Falha ao salvar imagem traduzida: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count imagens salvas com sucesso';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount salvas com sucesso, $failureCount falharam';
  }

  @override
  String errorDownloading(String error) {
    return 'Erro ao baixar: $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Falha ao carregar $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return 'Excluir $count item(ns)?';
  }

  @override
  String get drafts => 'Tarefas pendentes';

  @override
  String toLanguage(String lang) {
    return 'Para: $lang';
  }

  @override
  String get translateAll => 'Traduzir Todos';

  @override
  String get nothingSelected => 'Nada selecionado';

  @override
  String operationFailed(String error) {
    return 'Falha: $error';
  }

  @override
  String get allJobsDone => 'Todas as tarefas concluídas';

  @override
  String get imageNotFound => 'Imagem não encontrada';

  @override
  String usesLeftToday(int count) {
    return '$count usos restantes hoje';
  }

  @override
  String get upgradeToPro => 'Atualize para PRO para acesso ilimitado';

  @override
  String get upgradeNow => 'OBTER PRO';

  @override
  String get updatingTranslationPosition =>
      'Atualizando posição da tradução...';

  @override
  String get noImagesFound =>
      'Nenhuma imagem encontrada ou carregamento lento em andamento';

  @override
  String get unableToProcessImages =>
      'Não foi possível encontrar imagens para processar na página.';

  @override
  String downloadFailed(String error) {
    return 'Falha no download: $error';
  }

  @override
  String get selectAtLeastOneImage =>
      'Por favor, selecione pelo menos uma imagem';

  @override
  String get noValidImagesToDownload => 'Nenhuma imagem válida para baixar';

  @override
  String failedToProcessImages(String error) {
    return 'Falha ao processar imagens: $error';
  }

  @override
  String get loadingImages => 'Carregando imagens...';

  @override
  String get deleteThisItem => 'Excluir este item?';

  @override
  String get errorLoadingImageViewer =>
      'Erro ao carregar visualizador de imagens';

  @override
  String get failedToDeleteImage => 'Falha ao excluir imagem';

  @override
  String completedTranslationAt(String time) {
    return 'Tradução concluída em \n$time';
  }

  @override
  String get dailyLimitReached => 'Limite diário atingido';

  @override
  String get quotaResetMessage =>
      'Cota grátis renova amanhã. Veja anúncios para ganhar mais';

  @override
  String get upgradeButton => 'Atualizar';

  @override
  String get tryTomorrowButton => 'Tentar Amanhã';

  @override
  String get followSystem => 'Seguir sistema';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Desbloquear tradução de arquivos';

  @override
  String get highQualityTranslation => 'Tradução de alta qualidade';

  @override
  String get adFreeExperience => 'Experiência sem anúncios';

  @override
  String get getPro => 'OBTER PRO';

  @override
  String get loadFailed =>
      'Falha no carregamento, verifique sua conexão de rede';

  @override
  String get back => 'Voltar';

  @override
  String get purchaseSuccessful => 'Compra bem-sucedida';

  @override
  String get purchaseRestored => 'Compra restaurada';

  @override
  String restoreFailed(String error) {
    return 'Falha na restauração: $error';
  }

  @override
  String get weekly => 'Semanal';

  @override
  String get annual => 'Anual';

  @override
  String get free => 'GRÁTIS';

  @override
  String get freeText => 'Teste gratuito de 3 dias';

  @override
  String get billedMonthly => 'Cobrança mensal';

  @override
  String get billedAnnual => 'Cobrança anual';

  @override
  String get billedWeekly => 'Cobrança semanal';

  @override
  String get freeTrialText => 'Teste gratuito de 3 dias';

  @override
  String get save30Percent => 'Economize 30%';

  @override
  String get loginTitle => 'Entre na sua\n conta';

  @override
  String get watchAd => 'Obtener Más';

  @override
  String get watchAdDescription =>
      'Assista a um anúncio para obter mais cota de uso';

  @override
  String get fontSettings => 'Configurações de fonte';

  @override
  String get fontFamily => 'Família da fonte';

  @override
  String get strokeSettings => 'Configurações de contorno';

  @override
  String get textStyleSettings => 'Configurações de estilo de texto';

  @override
  String get resetToDefaults => 'Redefinir para padrão';

  @override
  String get fontSize => 'Tamanho da fonte';

  @override
  String get fontWeight => 'Peso da fonte';

  @override
  String get textColor => 'Cor do texto';

  @override
  String get textLabel => 'Texto';

  @override
  String get strokeLabel => 'Contorno';

  @override
  String get strokeWidth => 'Largura do contorno';

  @override
  String get shadowEffect => 'Efeito de sombra';

  @override
  String get opacity => 'Opacidade';

  @override
  String get horizontalOffset => 'Deslocamento horizontal';

  @override
  String get verticalOffset => 'Deslocamento vertical';

  @override
  String get blurRadius => 'Raio de desfoque';

  @override
  String get fontWeightThin => 'Fino';

  @override
  String get fontWeightNormal => 'Normal';

  @override
  String get fontWeightMedium => 'Médio';

  @override
  String get fontWeightSemiBold => 'Semi-negrito';

  @override
  String get fontWeightBold => 'Negrito';

  @override
  String get fontWeightExtraBold => 'Extra-negrito';

  @override
  String get fontWeightBlack => 'Preto';

  @override
  String get recommendedWebsites => 'Sites recomendados';

  @override
  String get myBookmarks => 'Meus favoritos';

  @override
  String get noBookmarks => 'Nenhum favorito';

  @override
  String get bookmarkAdded => 'Favorito adicionado';

  @override
  String get alreadyBookmarked => 'Já está nos favoritos';

  @override
  String get cannotBookmarkEmptyPage =>
      'Não é possível adicionar página vazia aos favoritos';

  @override
  String get bookmarkFailed => 'Falha ao adicionar favorito';

  @override
  String get deleteBookmark => 'Excluir favorito';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'Tem certeza de que deseja excluir o favorito \"$title\"?';
  }

  @override
  String get bookmarkDeleted => 'Favorito excluído';

  @override
  String get manageBookmarks => 'Gerenciar';

  @override
  String get allBookmarks => 'Todos os favoritos';

  @override
  String get browsingHistory => 'Histórico';

  @override
  String get noBrowsingHistory => 'Ainda não há histórico de navegação';

  @override
  String get deleteHistory => 'Excluir histórico';

  @override
  String get deleteHistoryConfirm =>
      'Tem certeza de que deseja excluir este item do histórico?';

  @override
  String get historyDeleted => 'Item do histórico excluído';

  @override
  String get justNow => 'Agora mesmo';

  @override
  String minutesAgo(int count) {
    return 'há $count minutos';
  }

  @override
  String hoursAgo(int count) {
    return 'há $count horas';
  }

  @override
  String daysAgo(int count) {
    return 'há $count dias';
  }
}
