import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/models/browser_tab.dart';
import 'package:imtrans/pages/browser/browser_tab_view.dart';
import 'package:imtrans/pages/browser/download.dart';
import 'package:imtrans/pages/browser/extractors/extractor_factory.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/services/bookmark_service.dart';
import 'package:imtrans/services/local_ocr_service.dart';
import 'package:imtrans/services/local_translation_service.dart';
import 'package:imtrans/services/webview_overlay_service.dart';
import 'package:imtrans/util/language_manager.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:typed_data';

class MultiBrowserPage extends StatefulWidget {
  // final String? initialSiteName;
  final String? initialUrl;

  const MultiBrowserPage({
    Key? key,
    // this.initialSiteName,
    this.initialUrl,
  }) : super(key: key);

  @override
  State<MultiBrowserPage> createState() => _MultiBrowserPageState();
}

class _MultiBrowserPageState extends State<MultiBrowserPage> with TickerProviderStateMixin {
  List<BrowserTab> _tabs = [];
  int _currentTabIndex = 0;
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _urlFocusNode = FocusNode();
  bool _isDownloading = false;
  bool _isLocalTranslating = false;
  late TabController _tabController;
  final Map<String, GlobalKey<BrowserTabViewState>> _tabKeys = {};

  // Local translation services
  final LocalOcrService _ocrService = LocalOcrService();
  final LocalTranslationService _translationService = LocalTranslationService();
  final WebViewOverlayService _overlayService = WebViewOverlayService();
  bool _servicesInitialized = false;

  // Translation toggle state
  bool _translationToggleEnabled = false;
  List<OcrTextElement> _currentTranslatedElements = [];

  // More menu state
  bool _isMoreMenuVisible = false;

  // 用于管理每个地址栏的状态
  final Map<String, TextEditingController> _addressControllers = {};
  final Map<String, FocusNode> _addressFocusNodes = {};
  String? _editingTabId; // 当前正在编辑的tab ID

  // 用于跟踪URL变化和动作按钮状态
  final Map<String, String> _lastKnownUrls = {}; // 跟踪每个tab的最后已知URL
  final Map<String, DateTime> _urlChangeTimestamps = {}; // 跟踪URL变化时间
  final ScrollController _tabScrollController = ScrollController(); // 控制tab滚动
  bool _canGoForwardState = false; // 跟踪前进按钮状态
  bool _canGoBackState = false; // 跟踪后退按钮状态

  @override
  void initState() {
    super.initState();
    _initializeBrowser();
    _initializeServices();
  }

  // 初始化浏览器状态
  Future<void> _initializeBrowser() async {
    // 创建初始tab - 总是从干净状态开始
    final initialTab = BrowserTab(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    _tabs.add(initialTab);
    _tabKeys[initialTab.id] = GlobalKey<BrowserTabViewState>();
    _createAddressBarControllers(initialTab.id);

    // 初始化TabController
    _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
    _tabController.addListener(_onTabChanged);

    // 智能处理初始URL
    if (widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
      _handleInitialUrl(widget.initialUrl!);
    } else {
      // 更新UI - 显示推荐网站页面
      setState(() {
        // 更新地址栏
        if (_currentTabIndex < _tabs.length) {
          _urlController.text = _tabs[_currentTabIndex].url;
        }
      });

      // 初始化后居中显示当前tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _centerCurrentTab();
      });
    }
  }

  // 智能处理初始URL
  void _handleInitialUrl(String initialUrl) {
    // 检查是否有空白tab可以重用
    bool hasEmptyTab = _tabs.any((tab) => tab.isEmpty);
    bool hasContentTab = _tabs.any((tab) => tab.hasContent);

    if (!hasContentTab || hasEmptyTab) {
      // 如果没有内容tab或有空白tab，重用第一个tab
      debugPrint('重用现有空白tab加载URL: $initialUrl');
      _loadUrlToTab(_tabs.first.id, initialUrl);
    } else {
      // 如果所有tab都有内容，创建新tab
      debugPrint('创建新tab加载URL: $initialUrl');
      _createNewTab(initialInput: initialUrl);
    }
  }

  // 加载URL到指定tab
  void _loadUrlToTab(String tabId, String url) {
    final tabIndex = _tabs.indexWhere((tab) => tab.id == tabId);
    if (tabIndex == -1) return;

    setState(() {
      _currentTabIndex = tabIndex;
      _tabController.animateTo(tabIndex);
      _urlController.text = url;

      // 更新地址栏控制器
      _addressControllers[tabId]?.text = url;
    });

    // 居中显示tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerCurrentTab();
    });

    // 延迟加载URL以确保WebView准备好
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        final tabViewState = _tabKeys[tabId]?.currentState;
        if (tabViewState != null) {
          tabViewState.loadUrl(url);
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _urlController.dispose();
    _urlFocusNode.dispose();
    _tabScrollController.dispose();

    // 清理所有地址栏控制器
    for (String tabId in _addressControllers.keys.toList()) {
      _disposeAddressBarControllers(tabId);
    }

    // 清理本地服务
    _ocrService.dispose();
    _translationService.dispose();
    _overlayService.dispose();

    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.index != _currentTabIndex && _tabController.index < _tabs.length) {
      final newTabIndex = _tabController.index;
      final newTab = _tabs[newTabIndex];

      // Clean up translation state when switching tabs
      _cleanupTranslationState();

      setState(() {
        _currentTabIndex = newTabIndex;
        _urlController.text = newTab.url;
      });

      // 居中显示新的当前tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _centerCurrentTab();
      });

      // 更新前进和后退按钮状态
      _updateForwardButtonState();
      _updateBackButtonState();

      // 强制更新UI以反映新tab的翻译按钮状态
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // 确保当前tab的WebView被创建
        await _ensureCurrentTabWebViewCreated();
        _refreshTranslateButtonState();
      });
    }
  }

  // 为tab创建地址栏控制器和焦点节点
  void _createAddressBarControllers(String tabId) {
    _addressControllers[tabId] = TextEditingController();
    _addressFocusNodes[tabId] = FocusNode();

    // 初始化URL跟踪
    _lastKnownUrls[tabId] = '';
    _urlChangeTimestamps[tabId] = DateTime.now();

    // 监听焦点变化
    _addressFocusNodes[tabId]!.addListener(() {
      if (_addressFocusNodes[tabId]!.hasFocus) {
        // 只在编辑状态真正改变时才调用setState
        if (_editingTabId != tabId) {
          setState(() {
            _editingTabId = tabId;
          });
          
          // 延迟更新文本内容，让UI先完成textAlign的变化
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
            if (tab.id.isNotEmpty && _addressControllers[tabId] != null) {
              _addressControllers[tabId]!.text = tab.url;
              _addressControllers[tabId]!.selection = TextSelection(
                baseOffset: 0,
                extentOffset: tab.url.length,
              );
            }
          });
        }
      } else {
        // 失去焦点时，恢复显示域名
        if (_editingTabId == tabId) {
          setState(() {
            _editingTabId = null;
          });
        }
      }
    });
  }

  // 清理tab的地址栏控制器
  void _disposeAddressBarControllers(String tabId) {
    _addressControllers[tabId]?.dispose();
    _addressFocusNodes[tabId]?.dispose();
    _addressControllers.remove(tabId);
    _addressFocusNodes.remove(tabId);
    _lastKnownUrls.remove(tabId);
    _urlChangeTimestamps.remove(tabId);

    if (_editingTabId == tabId) {
      _editingTabId = null;
    }
  }

  // 跟踪URL变化
  void _trackUrlChange(String tabId, String newUrl) {
    final oldUrl = _lastKnownUrls[tabId];
    if (oldUrl != newUrl) {
      _lastKnownUrls[tabId] = newUrl;
      _urlChangeTimestamps[tabId] = DateTime.now();
    }
  }

  // 判断是否应该显示搜索图标（而不是刷新图标）
  bool _shouldShowSearchIcon(String tabId) {
    final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
    if (tab.id.isEmpty) return true;

    // 如果正在加载，显示停止图标
    if (tab.isLoading) return false;

    // 如果URL为空或是新tab，显示搜索图标
    if (tab.url.isEmpty) return true;

    // 如果URL最近发生了变化（5秒内），显示搜索图标
    final lastChange = _urlChangeTimestamps[tabId];
    if (lastChange != null) {
      final timeSinceChange = DateTime.now().difference(lastChange);
      if (timeSinceChange.inSeconds < 5) {
        return true;
      }
    }

    // 否则显示刷新图标
    return false;
  }

  // 更新tab信息
  void _updateTab(BrowserTab updatedTab) {
    setState(() {
      final index = _tabs.indexWhere((tab) => tab.id == updatedTab.id);
      if (index != -1) {
        final oldTab = _tabs[index];

        // 跟踪URL变化
        _trackUrlChange(updatedTab.id, updatedTab.url);

        _tabs[index] = updatedTab;

        // 如果是当前tab且加载状态发生变化，触发UI更新
        // if (index == _currentTabIndex && oldTab.isLoading != updatedTab.isLoading) {
        // debugPrint('当前tab加载状态变化: ${updatedTab.isLoading ? "加载中" : "加载完成"}');
        // setState已经在外层调用，这里只需要记录日志
        // }

        // 如果是当前tab，更新地址栏和按钮状态
        if (index == _currentTabIndex) {
          _urlController.text = updatedTab.url;
          // 更新前进和后退按钮状态
          _updateForwardButtonState();
          _updateBackButtonState();

          // 如果controller状态发生变化，需要更新翻译按钮状态
          if (oldTab.controller != updatedTab.controller) {
            debugPrint('当前tab的controller状态发生变化，更新翻译按钮状态');
            // setState已经在外层调用，翻译按钮会自动重新评估
          }
        }

        // 更新对应的地址栏控制器（如果不在编辑状态）
        if (_editingTabId != updatedTab.id && _addressControllers[updatedTab.id] != null) {
          _addressControllers[updatedTab.id]!.text = updatedTab.url;
        }
      }
    });
  }

  // 更新前进按钮状态
  void _updateForwardButtonState() async {
    final canGoForward = await _canGoForward();
    if (mounted && _canGoForwardState != canGoForward) {
      setState(() {
        _canGoForwardState = canGoForward;
      });
    }
  }

  // 更新后退按钮状态
  void _updateBackButtonState() async {
    final canGoBack = await _canGoBack();
    if (mounted && _canGoBackState != canGoBack) {
      setState(() {
        _canGoBackState = canGoBack;
      });
    }
  }

  // 切换到指定tab并居中显示
  void _switchToTab(int index) {
    if (index < 0 || index >= _tabs.length || index == _currentTabIndex) return;

    setState(() {
      _currentTabIndex = index;
      _tabController.animateTo(index);
    });

    // 计算需要滚动的位置以居中显示当前tab
    _centerCurrentTab();

    // 更新前进和后退按钮状态
    _updateForwardButtonState();
    _updateBackButtonState();

    // 强制更新UI以反映新tab的翻译按钮状态
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 确保当前tab的WebView被创建
      await _ensureCurrentTabWebViewCreated();
      _refreshTranslateButtonState();
    });
  }

  // 将当前tab居中显示
  void _centerCurrentTab() {
    if (!_tabScrollController.hasClients || !mounted) return;

    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;
    // 计算tab宽度（80%屏幕宽度）
    final tabWidth = screenWidth * 0.8;

    // 计算当前tab的左边缘位置，考虑额外的边距
    double currentTabLeftEdge = 0;
    for (int i = 0; i < _currentTabIndex; i++) {
      // 每个tab的宽度
      currentTabLeftEdge += tabWidth;
      // 添加边距：第一个tab左侧40px，最后一个tab右侧40px，其他tab右侧8px
      if (i == 0) {
        currentTabLeftEdge += 40; // 第一个tab的左边距
      }
      if (i == _tabs.length - 1) {
        currentTabLeftEdge += 40; // 最后一个tab的右边距
      } else {
        currentTabLeftEdge += 8; // 普通tab的右边距
      }
    }

    // 如果当前tab是第一个，需要加上它的左边距
    if (_currentTabIndex == 0) {
      currentTabLeftEdge += 40;
    }

    // 计算屏幕中心位置
    final screenCenter = screenWidth / 2;

    // 计算tab中心位置
    final tabCenter = tabWidth / 2;

    // 计算目标滚动位置（使当前tab的中心对齐屏幕中心）
    final targetOffset = currentTabLeftEdge + tabCenter - screenCenter;

    // 确保滚动位置在有效范围内
    final maxOffset = _tabScrollController.position.maxScrollExtent;
    final minOffset = _tabScrollController.position.minScrollExtent;
    final clampedOffset = targetOffset.clamp(minOffset, maxOffset);

    // 平滑滚动到目标位置
    if (_tabScrollController.offset != clampedOffset) {
      _tabScrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // 创建新tab
  void _createNewTab({String? initialInput}) {
    final newTab = BrowserTab(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
    );

    setState(() {
      _tabs.add(newTab);
      _tabKeys[newTab.id] = GlobalKey<BrowserTabViewState>();
      _createAddressBarControllers(newTab.id);
      _currentTabIndex = _tabs.length - 1;

      // 重新创建TabController
      _tabController.removeListener(_onTabChanged);
      _tabController.dispose();
      _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
      _tabController.addListener(_onTabChanged);

      _urlController.text = initialInput ?? '';
    });

    // 居中显示新tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerCurrentTab();
    });

    // 如果有初始输入，延迟加载以确保WebView已准备好
    if (initialInput != null && initialInput.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadUrl(initialInput);
        }
      });
    }
  }

  // 关闭tab
  void _closeTab(int index) {
    if (index < 0 || index >= _tabs.length) {
      debugPrint('无效的tab索引: $index');
      return;
    }

    if (_tabs.length <= 1) {
      // 如果只有一个tab，关闭整个浏览器
      debugPrint('关闭最后一个tab，返回主页');
      Navigator.of(context).pop();
      return;
    }

    final tabToRemove = _tabs[index];

    // 标记tab为已dispose，防止后续使用
    tabToRemove.markAsDisposed();

    // 不要手动dispose WebView controller
    // InAppWebView会在其Widget dispose时自动释放controller
    // 手动dispose可能导致controller被重复释放或在异步操作中使用已dispose的controller

    setState(() {
      _tabs.removeAt(index);
      _tabKeys.remove(tabToRemove.id);
      _disposeAddressBarControllers(tabToRemove.id);

      // 调整当前tab索引
      if (_currentTabIndex >= _tabs.length) {
        _currentTabIndex = _tabs.length - 1;
      } else if (_currentTabIndex > index) {
        _currentTabIndex--;
      }

      // 确保_currentTabIndex不会为负数
      if (_currentTabIndex < 0) {
        _currentTabIndex = 0;
      }

      // 重新创建TabController
      _tabController.removeListener(_onTabChanged);
      _tabController.dispose();

      if (_tabs.isNotEmpty) {
        _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
        _tabController.addListener(_onTabChanged);

        // 更新地址栏
        if (_currentTabIndex < _tabs.length) {
          _urlController.text = _tabs[_currentTabIndex].url;
        }
      }
    });
  }

  // 从地址栏加载URL到指定tab
  Future<void> _loadUrlFromAddressBar(String tabId) async {
    final tabIndex = _tabs.indexWhere((tab) => tab.id == tabId);
    if (tabIndex == -1) return;

    final controller = _addressControllers[tabId];
    if (controller == null) return;

    final inputText = controller.text.trim();
    if (inputText.isEmpty) return;

    // 切换到该tab
    setState(() {
      _currentTabIndex = tabIndex;
      _tabController.animateTo(tabIndex);
    });

    // 加载URL
    final tabViewState = _tabKeys[tabId]?.currentState;
    if (tabViewState != null) {
      await tabViewState.loadUrl(inputText);
    }

    // 失去焦点
    _addressFocusNodes[tabId]?.unfocus();
  }

  // 加载URL到当前tab
  Future<void> _loadUrl([String? url]) async {
    if (_currentTabIndex >= _tabs.length) return;

    final currentTab = _tabs[_currentTabIndex];
    final tabViewState = _tabKeys[currentTab.id]?.currentState;

    if (tabViewState != null) {
      final inputText = url ?? _urlController.text.trim();
      if (inputText.isNotEmpty) {
        await tabViewState.loadUrl(inputText);
      }
    } else {
      // 如果TabView还没有准备好，延迟重试
      debugPrint('TabView还没有准备好，延迟重试加载URL');
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _currentTabIndex < _tabs.length) {
          _loadUrl(url);
        }
      });
    }
  }

  // 刷新指定tab
  void _refreshTab(String tabId) async {
    final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
    if (tab.id.isEmpty || tab.isDisposed || !mounted) return;

    debugPrint('刷新tab: ${tab.title} (${tab.url})');

    try {
      // 如果controller不存在，说明WebView还没创建，需要先确保WebView被创建
      if (tab.controller == null) {
        debugPrint('Controller不存在，先确保WebView被创建');

        // 如果不是当前tab，先切换到该tab
        final tabIndex = _tabs.indexWhere((t) => t.id == tabId);
        if (tabIndex != -1 && tabIndex != _currentTabIndex) {
          _switchToTab(tabIndex);
          // 等待tab切换完成
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // 等待WebView controller创建
        await _ensureCurrentTabWebViewCreated();

        // 重新获取最新的tab状态
        final latestTab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
        if (latestTab.controller != null) {
          await latestTab.controller!.reload();
          debugPrint('WebView创建后刷新成功');
        } else {
          // 如果还是没有controller，使用loadUrl作为备选方案
          debugPrint('Controller仍然不存在，使用loadUrl重新加载');
          if (latestTab.url.isNotEmpty) {
            await latestTab.controller?.loadUrl(urlRequest: URLRequest(url: WebUri(latestTab.url)));
          }
        }
      } else {
        // Controller存在，直接刷新
        await tab.controller!.reload();
        debugPrint('直接刷新成功');
      }

      // 标记URL变化以显示搜索图标
      _trackUrlChange(tabId, tab.url);
    } catch (e) {
      debugPrint('刷新页面时出错: $e');

      // 如果刷新失败，尝试重新加载URL
      try {
        if (tab.url.isNotEmpty && tab.controller != null) {
          await tab.controller!.loadUrl(urlRequest: URLRequest(url: WebUri(tab.url)));
          debugPrint('重新加载URL成功');
        }
      } catch (e2) {
        debugPrint('重新加载URL也失败: $e2');
      }
    }
  }

  // 停止加载
  void _stopLoading() {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          currentTab.controller?.stopLoading();
        } catch (e) {
          debugPrint('停止加载时出错: $e');
        }
      }

      if (mounted) {
        setState(() {
          _tabs[_currentTabIndex] = currentTab.copyWith(isLoading: false);
        });
      }
    }
  }

  // 后退
  Future<void> _goBack() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          if (await currentTab.controller?.canGoBack() ?? false) {
            if (mounted && !currentTab.isDisposed) {
              currentTab.controller?.goBack();
            }
          }
        } catch (e) {
          debugPrint('后退时出错: $e');
        }
      }
    }
  }

  // 前进
  Future<void> _goForward() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          if (await currentTab.controller?.canGoForward() ?? false) {
            if (mounted && !currentTab.isDisposed) {
              currentTab.controller?.goForward();
            }
          }
        } catch (e) {
          debugPrint('前进时出错: $e');
        }
      }
    }
  }

  // 检查当前tab是否可以前进
  Future<bool> _canGoForward() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          return await currentTab.controller?.canGoForward() ?? false;
        } catch (e) {
          debugPrint('检查前进状态时出错: $e');
          return false;
        }
      }
    }
    return false;
  }

  // 检查当前tab是否可以后退
  Future<bool> _canGoBack() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          return await currentTab.controller?.canGoBack() ?? false;
        } catch (e) {
          debugPrint('检查后退状态时出错: $e');
          return false;
        }
      }
    }
    return false;
  }

  // 返回主页
  void _goHome() {
    // 返回主页
    Navigator.of(context).pop();
  }

  // 从URL提取域名用于显示
  String _extractDomainFromUrl(String url) {
    if (url.isEmpty) return '';

    try {
      final uri = Uri.parse(url);
      String host = uri.host;

      // 移除 www. 前缀
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      return host.isNotEmpty ? host : url;
    } catch (e) {
      // 如果解析失败，返回原始URL
      return url;
    }
  }

  // 开始下载
  Future<void> _startDownload(BuildContext context) async {
    if (_currentTabIndex >= _tabs.length) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      final currentTab = _tabs[_currentTabIndex];

      debugPrint('开始翻译 - 当前tab: ${currentTab.title}, URL: ${currentTab.url}');
      debugPrint(
          'Controller状态: ${currentTab.controller != null ? "存在" : "null"}, isDisposed: ${currentTab.isDisposed}');

      // 检查基本条件
      if (currentTab.url.isEmpty || currentTab.url == 'about:blank') {
        throw Exception('Please navigate to a webpage first');
      }

      // 检查并等待WebView controller
      if (currentTab.controller == null || currentTab.isDisposed) {
        debugPrint('WebView controller不可用，可能是TabBarView还没创建该tab的WebView');

        // 强制切换到当前tab以触发WebView创建
        if (_tabController.index != _currentTabIndex) {
          debugPrint('强制切换到当前tab以创建WebView');
          _tabController.animateTo(_currentTabIndex);

          // 等待TabBarView切换完成
          await Future.delayed(const Duration(milliseconds: 300));
        }

        // 等待WebView controller初始化（最多3秒）
        bool controllerReady = false;
        for (int i = 0; i < 30; i++) {
          await Future.delayed(const Duration(milliseconds: 100));
          // 重新获取最新的tab状态
          final latestTab = _tabs[_currentTabIndex];
          if (latestTab.controller != null && !latestTab.isDisposed) {
            controllerReady = true;
            debugPrint('WebView controller已准备好');
            break;
          }
        }

        if (!controllerReady) {
          throw Exception('WebView controller not available. The page may need more time to load.');
        }
      }

      // 重新获取最新的tab状态，确保controller是最新的
      final latestTab = _tabs[_currentTabIndex];
      final extractor = ExtractorFactory.createExtractor(
        controller: latestTab.controller!,
        currentUrl: latestTab.url,
      );

      final urls = await extractor.extractImageUrls();
      debugPrint("Browser提取到 ${urls.length} 个图片URL：$urls");

      if (urls.isEmpty) {
        ToastWidget.show(AppLocalizations.of(context)!.noImagesFound);
      } else {
        final needsWebView = extractor.needsWebViewForImages();

        if (needsWebView) {
          debugPrint("检测到需要使用WebView获取图片的网站: ${currentTab.url}");

          final images = await extractor.downloadImages(urls);
          debugPrint("WebView获取到 ${images.length} 张有效图片");

          if (images.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (_) {
                return BrowserDownloadPage(
                  imageUrls: urls.sublist(0, images.length),
                  preloadedImages: images,
                );
              }),
            );
          } else {
            ToastWidget.show(AppLocalizations.of(context)!.unableToProcessImages);
          }
        } else {
          debugPrint("检测到不需要使用WebView获取图片的网站: ${currentTab.url}");
          final String cookies = await extractor.getCookies();
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) {
              return BrowserDownloadPage(
                imageUrls: urls,
                cookies: cookies,
                referer: currentTab.url,
              );
            }),
          );
        }
      }
    } catch (e) {
      debugPrint("Download Error: $e");
      if (mounted) {
        ToastWidget.showTop(AppLocalizations.of(context)!.downloadFailed(e.toString()));

        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //   content:
        //       Text(AppLocalizations.of(context)!.downloadFailed(e.toString())),
        //   duration: const Duration(seconds: 4),
        //   action: SnackBarAction(
        //     label: AppLocalizations.of(context)!.retry,
        //     onPressed: () => _startDownload(context),
        //   ),
        // ));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  // 检查当前tab是否可以进行翻译操作
  bool _canTranslateCurrentTab() {
    if (_currentTabIndex >= _tabs.length) {
      debugPrint('翻译按钮检查: 当前tab索引超出范围');
      return false;
    }

    final currentTab = _tabs[_currentTabIndex];

    // 检查基本条件：有URL且tab未销毁
    // 注意：不强制要求controller存在，因为TabBarView可能还没创建WebView
    final hasValidUrl = currentTab.url.isNotEmpty && currentTab.url != 'about:blank';
    final isNotDisposed = !currentTab.isDisposed;

    // debugPrint('翻译按钮状态检查 - Tab: ${currentTab.title}');
    // debugPrint('  URL有效: $hasValidUrl (${currentTab.url})');
    // debugPrint('  Controller存在: ${currentTab.controller != null}');
    // debugPrint('  未销毁: $isNotDisposed');

    // 如果有有效URL且tab未销毁，就认为可以尝试翻译
    // controller会在实际执行翻译时检查和等待
    if (!hasValidUrl || !isNotDisposed) {
      return false;
    }

    return true;
  }

  // 刷新翻译按钮状态
  void _refreshTranslateButtonState() {
    if (mounted) {
      setState(() {
        // 这个setState会触发build方法重新执行
        // 从而重新评估_canTranslateCurrentTab()的结果
      });
    }
  }

  // 初始化本地服务
  Future<void> _initializeServices() async {
    try {
      await _ocrService.initialize();
      await _translationService.initialize();
      await _overlayService.initialize();
      _servicesInitialized = true;
      debugPrint('MultiBrowserPage: Local services initialized successfully');
    } catch (e) {
      debugPrint('MultiBrowserPage: Failed to initialize local services - $e');
      _servicesInitialized = false;
    }
  }

  // 切换本地翻译
  Future<void> _toggleLocalTranslation(BuildContext context) async {
    if (_currentTabIndex >= _tabs.length) return;

    if (!_servicesInitialized) {
      ToastWidget.show(AppLocalizations.of(context)!.loading);
      return;
    }

    // If translation is currently enabled, disable it
    if (_translationToggleEnabled) {
      await _disableTranslation();
      return;
    }

    // Otherwise, enable translation
    await _enableTranslation(context);
  }

  // 启用翻译
  Future<void> _enableTranslation(BuildContext context) async {
    setState(() {
      _isLocalTranslating = true;
    });

    try {
      final currentTab = _tabs[_currentTabIndex];

      // 确保WebView controller可用
      bool controllerReady = false;
      int attempts = 0;
      const maxAttempts = 10;

      while (!controllerReady && attempts < maxAttempts) {
        await _ensureCurrentTabWebViewCreated();
        controllerReady = currentTab.controller != null && !currentTab.isDisposed;

        if (!controllerReady) {
          await Future.delayed(const Duration(milliseconds: 500));
          attempts++;
        }
      }

      if (!controllerReady) {
        throw Exception('WebView controller not available. The page may need more time to load.');
      }

      // Set the overlay service controller
      _overlayService.setController(currentTab.controller!);

      // Test OCR service before processing
      final ocrTestResult = await _ocrService.testOcrService();
      debugPrint("OCR service test result: $ocrTestResult");
      if (!ocrTestResult) {
        ToastWidget.show("OCR service not available");
        return;
      }

      // 获取页面中的所有图片
      final extractor = ExtractorFactory.createExtractor(
        controller: currentTab.controller!,
        currentUrl: currentTab.url,
      );

      final imageUrls = await extractor.extractImageUrls();
      debugPrint("Local translation: Found ${imageUrls.length} images");

      if (imageUrls.isEmpty) {
        ToastWidget.show(AppLocalizations.of(context)!.noImagesFound);
        return;
      }

      // 下载图片并进行OCR和翻译
      final translatedElements = await _processImagesForTranslation(imageUrls, extractor, context);

      if (translatedElements.isNotEmpty) {
        // Store translated elements and show overlays
        _currentTranslatedElements = translatedElements;
        await _overlayService.showOverlays(translatedElements, imageUrls);

        setState(() {
          _translationToggleEnabled = true;
        });

        ToastWidget.show("Translation enabled with ${translatedElements.length} text elements");
      }

    } catch (e) {
      debugPrint('Local translation error: $e');
      ToastWidget.show('Translation failed: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLocalTranslating = false;
        });
      }
    }
  }

  // 禁用翻译
  Future<void> _disableTranslation() async {
    try {
      await _overlayService.hideOverlays();
      _currentTranslatedElements.clear();

      setState(() {
        _translationToggleEnabled = false;
      });

      ToastWidget.show("Translation disabled");
    } catch (e) {
      debugPrint('Error disabling translation: $e');
    }
  }

  // 清理翻译状态（用于标签页切换和页面导航）
  void _cleanupTranslationState() {
    if (_translationToggleEnabled) {
      _overlayService.hideOverlays().catchError((e) {
        debugPrint('Error cleaning up overlays: $e');
      });
      _currentTranslatedElements.clear();
      _translationToggleEnabled = false;
    }
  }

  // 将语言代码转换为TranslateLanguage枚举
  TranslateLanguage? _getTranslateLanguage(String languageCode) {
    switch (languageCode.toLowerCase()) {
      case 'en':
        return TranslateLanguage.english;
      case 'ja':
        return TranslateLanguage.japanese;
      case 'ko':
        return TranslateLanguage.korean;
      case 'zh':
        return TranslateLanguage.chinese;
      case 'fr':
        return TranslateLanguage.french;
      case 'de':
        return TranslateLanguage.german;
      case 'es':
        return TranslateLanguage.spanish;
      case 'it':
        return TranslateLanguage.italian;
      case 'pt':
        return TranslateLanguage.portuguese;
      case 'th':
        return TranslateLanguage.thai;
      case 'vi':
        return TranslateLanguage.vietnamese;
      case 'id':
        return TranslateLanguage.indonesian;
      case 'ms':
        return TranslateLanguage.malay;
      default:
        debugPrint('Unsupported language code: $languageCode, falling back to English');
        return TranslateLanguage.english;
    }
  }

  // 获取用户配置的目标语言
  TranslateLanguage _getTargetLanguage() {
    final targetLanguageCode = LanguageManager.currentLanguageCode;
    return _getTranslateLanguage(targetLanguageCode) ?? TranslateLanguage.english;
  }

  // 过滤主要内容图片，排除小图标和装饰性图片
  Future<List<String>> _filterMainContentImages(List<String> imageUrls, dynamic extractor) async {
    final filteredUrls = <String>[];

    debugPrint("Starting image filtering for ${imageUrls.length} images");

    for (final imageUrl in imageUrls) {
      try {
        // 基于URL过滤：排除明显的图标和小图片
        if (_isLikelyIconOrSmallImage(imageUrl)) {
          debugPrint("Skipping likely icon/small image: $imageUrl");
          continue;
        }

        // 优先基于URL判断是否为主要内容
        if (_isLikelyMainContentImage(imageUrl)) {
          debugPrint("URL indicates main content image: $imageUrl");

          // 尝试获取图片尺寸信息进行验证
          final imageSize = await _getImageSize(imageUrl, extractor);
          if (imageSize != null) {
            // 过滤掉太小的图片（可能是图标）
            if (imageSize.width < 80 || imageSize.height < 80) {
              debugPrint("Skipping small image (${imageSize.width}x${imageSize.height}): $imageUrl");
              continue;
            }

            // 过滤掉长宽比异常的图片（可能是装饰性图片）
            final aspectRatio = imageSize.width / imageSize.height;
            if (aspectRatio > 8 || aspectRatio < 0.125) {
              debugPrint("Skipping image with unusual aspect ratio ($aspectRatio): $imageUrl");
              continue;
            }

            // 添加通过尺寸验证的图片
            filteredUrls.add(imageUrl);
            debugPrint("Added verified content image (${imageSize.width}x${imageSize.height}): $imageUrl");
          } else {
            // 如果无法获取尺寸，但URL看起来像内容图片，也包含进来
            filteredUrls.add(imageUrl);
            debugPrint("Added likely main content image (no size info): $imageUrl");
          }
        }
      } catch (e) {
        debugPrint("Error filtering image $imageUrl: $e");
        // 出错时保守处理，包含该图片
        if (_isLikelyMainContentImage(imageUrl)) {
          filteredUrls.add(imageUrl);
        }
      }
    }

    // 按图片URL中的数字排序（通常漫画页面按顺序命名）
    filteredUrls.sort((a, b) {
      final aNum = _extractNumberFromUrl(a);
      final bNum = _extractNumberFromUrl(b);
      return aNum.compareTo(bNum);
    });

    return filteredUrls;
  }

  // 判断是否为图标或小图片（基于URL特征）
  bool _isLikelyIconOrSmallImage(String imageUrl) {
    final url = imageUrl.toLowerCase();

    // 常见图标关键词
    final iconKeywords = [
      'icon', 'logo', 'favicon', 'avatar', 'thumb', 'thumbnail',
      'button', 'arrow', 'star', 'heart', 'like', 'share',
      'comment', 'menu', 'header', 'footer', 'banner',
      'advertisement', 'sponsor', 'widget',
      'interface', 'control', 'social', 'badge'
    ];

    // 检查URL中是否包含图标关键词
    for (final keyword in iconKeywords) {
      if (url.contains(keyword)) {
        debugPrint("Likely icon/small image based on keyword '$keyword': $imageUrl");
        return true;
      }
    }

    // // 检查文件名中的尺寸信息（如 icon_24x24.png）
    // final sizePattern = RegExp(r'(\d+)x(\d+)');
    // final match = sizePattern.firstMatch(url);
    // if (match != null) {
    //   final width = int.tryParse(match.group(1) ?? '0') ?? 0;
    //   final height = int.tryParse(match.group(2) ?? '0') ?? 0;
    //   if (width < 100 || height < 100) {
    //     debugPrint("Likely icon/small image based on size in URL: $imageUrl");
    //     return true;
    //   }
    // }

    // 检查常见的小图片文件名模式
    // final smallImagePatterns = [
    //   RegExp(r'_\d{1,2}\.'), // 如 image_1.jpg, icon_24.png
    //   RegExp(r'small'), // 包含 small 的文件名
    //   RegExp(r'mini'), // 包含 mini 的文件名
    //   RegExp(r'tiny'), // 包含 tiny 的文件名
    // ];

    // for (final pattern in smallImagePatterns) {
    //   if (pattern.hasMatch(url)) {
    //     return true;
    //   }
    // }

    return false;
  }

  // 判断是否为主要内容图片
  bool _isLikelyMainContentImage(String imageUrl) {
    final url = imageUrl.toLowerCase();

    // 漫画/内容相关关键词
    final contentKeywords = [
      'manga', 'comic', 'chapter', 'page', 'content', 'main',
      'story', 'episode', 'part', 'vol', 'volume', 'data',
      'webtoon', 'manhwa', 'manhua'
    ];

    // 检查是否包含内容关键词
    for (final keyword in contentKeywords) {
      if (url.contains(keyword)) {
        return true;
      }
    }

    // 检查文件名模式（如包含长数字串的通常是内容图片）
    final longNumberPattern = RegExp(r'\d{10,}'); // 10位以上数字
    if (longNumberPattern.hasMatch(url)) {
      return true;
    }

    // 检查文件扩展名（优先处理常见图片格式）
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    final hasImageExtension = imageExtensions.any((ext) => url.endsWith(ext));

    // 检查文件大小指示（通常内容图片文件名包含较大的数字）
    final sizePattern = RegExp(r'(\d+)x(\d+)');
    final match = sizePattern.firstMatch(url);
    if (match != null) {
      final width = int.tryParse(match.group(1) ?? '0') ?? 0;
      final height = int.tryParse(match.group(2) ?? '0') ?? 0;
      // 如果尺寸较大，很可能是内容图片
      if (width >= 300 && height >= 300) {
        return true;
      }
    }

    // 如果是图片格式且不是明显的图标，则认为是内容图片
    return hasImageExtension && !_isLikelyIconOrSmallImage(imageUrl);
  }

  // 从URL中提取数字（用于排序）
  int _extractNumberFromUrl(String url) {
    final numberPattern = RegExp(r'(\d+)');
    final matches = numberPattern.allMatches(url);

    if (matches.isNotEmpty) {
      // 取最后一个数字（通常是页码或序号）
      final lastMatch = matches.last;
      return int.tryParse(lastMatch.group(1) ?? '0') ?? 0;
    }

    return 0;
  }

  // 获取图片尺寸信息
  Future<Size?> _getImageSize(String imageUrl, dynamic extractor) async {
    try {
      // 尝试通过JavaScript获取图片的自然尺寸
      if (_currentTabIndex < _tabs.length) {
        final currentTab = _tabs[_currentTabIndex];
        if (currentTab.controller != null && !currentTab.isDisposed) {
          final result = await currentTab.controller!.evaluateJavascript(source: '''
            (function() {
              var imgs = document.querySelectorAll('img');
              for (var i = 0; i < imgs.length; i++) {
                if (imgs[i].src === '$imageUrl') {
                  return {
                    width: imgs[i].naturalWidth || imgs[i].width,
                    height: imgs[i].naturalHeight || imgs[i].height
                  };
                }
              }
              return null;
            })();
          ''');

          if (result != null && result is Map) {
            final width = (result['width'] as num?)?.toDouble() ?? 0;
            final height = (result['height'] as num?)?.toDouble() ?? 0;
            if (width > 0 && height > 0) {
              return Size(width, height);
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting image size for $imageUrl: $e');
    }

    return null;
  }

  // 处理图片进行OCR和翻译
  Future<List<OcrTextElement>> _processImagesForTranslation(
    List<String> imageUrls,
    dynamic extractor,
    BuildContext context
  ) async {
    final allTranslatedElements = <OcrTextElement>[];

    try {
      // 过滤图片：只处理可能包含文本内容的主要图片
      final filteredUrls = await _filterMainContentImages(imageUrls, extractor);
      debugPrint("Filtered ${imageUrls.length} images to ${filteredUrls.length} main content images");

      // 限制处理的图片数量以避免性能问题
      final urlsToProcess = filteredUrls.take(5).toList();

      for (int i = 0; i < urlsToProcess.length; i++) {
        final imageUrl = urlsToProcess[i];
        debugPrint("Processing image ${i + 1}/${urlsToProcess.length}: $imageUrl");

        try {
          // 下载图片
          Uint8List? imageBytes;

          if (extractor.needsWebViewForImages()) {
            final images = await extractor.downloadImages([imageUrl]);
            if (images.isNotEmpty) {
              imageBytes = images[0];
            }
          } else {
            // 使用HTTP下载单张图片
            try {
              imageBytes = await extractor.getImageData(imageUrl, useWebView: false);
            } catch (e) {
              debugPrint("HTTP download failed for $imageUrl: $e");
              continue;
            }
          }

          if (imageBytes == null) {
            debugPrint("Failed to download image: $imageUrl");
            continue;
          }

          debugPrint("Successfully downloaded image: $imageUrl (${imageBytes.length} bytes)");

          // 获取图片尺寸用于坐标转换
          final imageSize = await _getImageSize(imageUrl, extractor);

          // 使用OCR提取文本
          try {
            final textElements = await _ocrService.extractTextFromBytes(imageBytes, imageSize: imageSize);
            debugPrint("OCR processing completed for $imageUrl: ${textElements.length} text elements found");

            if (textElements.isEmpty) {
              debugPrint("No text found in image: $imageUrl");
              continue;
            }

            debugPrint("Found ${textElements.length} text elements in image");

            // 翻译文本
            final translatedElements = <OcrTextElement>[];
            final targetLanguage = _getTargetLanguage();
            debugPrint("Using target language: ${targetLanguage.bcpCode}");

            for (final element in textElements) {
              try {
                final translatedText = await _translationService.translateText(
                  element.text,
                  TranslateLanguage.japanese, // Assume source is Japanese for now
                  targetLanguage,  // Use user's configured target language
                );

                translatedElements.add(OcrTextElement(
                  text: translatedText,
                  boundingBox: element.boundingBox,
                  confidence: element.confidence,
                ));
              } catch (e) {
                debugPrint("Translation failed for text '${element.text}': $e");
                // Keep original text if translation fails
                translatedElements.add(element);
              }
            }

            // Add translated elements to the collection
            allTranslatedElements.addAll(translatedElements);
            debugPrint("Translation completed for image $imageUrl with ${translatedElements.length} elements");
          } catch (e) {
            debugPrint("OCR processing failed for $imageUrl: $e");
            continue;
          }

        } catch (e) {
          debugPrint("Error processing image $imageUrl: $e");
          continue;
        }
      }

      debugPrint("Translation completed for ${urlsToProcess.length} images with ${allTranslatedElements.length} total elements");
      return allTranslatedElements;

    } catch (e) {
      debugPrint("Error in _processImagesForTranslation: $e");
      throw e;
    }
  }

  // 确保当前tab的WebView被创建
  Future<void> _ensureCurrentTabWebViewCreated() async {
    if (_currentTabIndex >= _tabs.length) return;

    final currentTab = _tabs[_currentTabIndex];

    // 如果controller已存在，直接返回
    if (currentTab.controller != null && !currentTab.isDisposed) {
      return;
    }

    debugPrint('确保当前tab的WebView被创建: ${currentTab.title}');

    // 确保TabBarView切换到当前tab
    if (_tabController.index != _currentTabIndex) {
      _tabController.animateTo(_currentTabIndex);
      await Future.delayed(const Duration(milliseconds: 300));
    }

    // 等待WebView创建
    for (int i = 0; i < 30; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      final latestTab = _tabs[_currentTabIndex];
      if (latestTab.controller != null && !latestTab.isDisposed) {
        debugPrint('WebView创建成功');
        return;
      }
    }

    debugPrint('WebView创建超时');
  }

  // 书签管理功能 - 切换书签状态（添加或移除）
  Future<void> _toggleBookmark() async {
    if (_currentTabIndex >= _tabs.length) return;

    final currentTab = _tabs[_currentTabIndex];
    if (currentTab.url.isEmpty || currentTab.url == 'about:blank') {
      return;
    }

    try {
      final isAdded = await BookmarkService.instance.toggleBookmark(
        currentTab.url,
        currentTab.title.isNotEmpty ? currentTab.title : currentTab.url,
        favicon: null, // 可以在这里添加favicon逻辑
      );

      if (isAdded) {
        ToastWidget.show(AppLocalizations.of(context)!.bookmarkAdded);
        debugPrint('书签已添加: ${currentTab.title} - ${currentTab.url}');
      } else {
        ToastWidget.show(AppLocalizations.of(context)!.bookmarkDeleted);
        debugPrint('书签已移除: ${currentTab.title} - ${currentTab.url}');
      }

      // 触发UI更新以反映书签状态变化
      setState(() {});
    } catch (e) {
      debugPrint('书签操作失败: $e');
      ToastWidget.show(AppLocalizations.of(context)!.bookmarkFailed);
    }
  }

  // 检查当前页面是否已收藏
  Future<bool> _isCurrentPageBookmarked() async {
    if (_currentTabIndex >= _tabs.length) return false;

    final currentTab = _tabs[_currentTabIndex];
    if (currentTab.url.isEmpty) return false;

    try {
      return await BookmarkService.instance.isBookmarked(currentTab.url);
    } catch (e) {
      debugPrint('检查书签状态失败: $e');
      return false;
    }
  }

  // 检查当前页面是否可以被收藏
  bool _canBookmarkCurrentPage() {
    if (_currentTabIndex >= _tabs.length) return false;

    final currentTab = _tabs[_currentTabIndex];

    // 检查URL是否为空或是新标签页
    if (currentTab.url.isEmpty ||
        currentTab.url == 'about:blank' ||
        currentTab.isEmpty) {
      return false;
    }

    // 检查是否为有效的HTTP/HTTPS URL
    try {
      final uri = Uri.parse(currentTab.url);
      return uri.scheme == 'http' || uri.scheme == 'https';
    } catch (e) {
      return false;
    }
  }

  // 显示更多菜单
  void _showMoreMenu() {
    setState(() {
      _isMoreMenuVisible = true;
    });

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return _buildMoreMenu();
      },
    ).then((_) {
      setState(() {
        _isMoreMenuVisible = false;
      });
    });
  }

  // 构建更多菜单
  Widget _buildMoreMenu() {
    final themeColors = ThemeManager.currentTheme;

    return Container(
      decoration: BoxDecoration(
        color: themeColors.backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              decoration: BoxDecoration(
                color: themeColors.textColor.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 菜单项
            _buildMoreMenuItem(
              icon: Icons.add,
              title: AppLocalizations.of(context)!.newTab,
              onTap: () {
                Navigator.pop(context);
                _createNewTab();
              },
            ),

            FutureBuilder<bool>(
              future: _isCurrentPageBookmarked(),
              builder: (context, snapshot) {
                final isBookmarked = snapshot.data ?? false;
                final canBookmark = _canBookmarkCurrentPage();
                return _buildMoreMenuItem(
                  icon: isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                  title: isBookmarked
                    ? AppLocalizations.of(context)!.removeBookmark
                    : AppLocalizations.of(context)!.addBookmark,
                  onTap: canBookmark ? () {
                    Navigator.pop(context);
                    _toggleBookmark();
                  } : null,
                );
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 构建更多菜单项
  Widget _buildMoreMenuItem({
    required IconData icon,
    required String title,
    VoidCallback? onTap,
  }) {
    final themeColors = ThemeManager.currentTheme;
    final isEnabled = onTap != null;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: isEnabled
                ? themeColors.textColor
                : themeColors.textColor.withValues(alpha: 0.3),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: isEnabled
                  ? themeColors.textColor
                  : themeColors.textColor.withValues(alpha: 0.3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeColors = ThemeManager.currentTheme;

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // 用户通过返回按钮离开浏览器
          debugPrint('用户离开浏览器页面');
        }
      },
      child: Scaffold(
        backgroundColor: themeColors.backgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              // WebView内容区域 - 现在占据顶部到底部工具栏之间的全部空间
              Expanded(
                child: _tabs.isEmpty
                    ? const Center(child: Text('No tabs available'))
                    : TabBarView(
                        controller: _tabController,
                        physics: const NeverScrollableScrollPhysics(), // 禁用左右滑动切换tab
                        children: _tabs.asMap().entries.map((entry) {
                          final index = entry.key;
                          final tab = entry.value;

                          // 智能处理initialInput
                          String? initialInput;
                          if (index == 0 && widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
                            // 新的初始URL（优先级最高）
                            initialInput = widget.initialUrl;
                            debugPrint('Tab $index: 使用新的初始URL: $initialInput');
                          } else if (tab.url.isNotEmpty) {
                            // 恢复的tab或有URL的tab，传递保存的URL
                            initialInput = tab.url;
                            debugPrint('Tab $index: 使用恢复的URL: $initialInput');
                          } else {
                            debugPrint('Tab $index: 无URL，将显示推荐站点');
                          }

                          return BrowserTabView(
                            key: _tabKeys[tab.id],
                            tab: tab,
                            onTabUpdated: _updateTab,
                            initialInput: initialInput,
                          );
                        }).toList(),
                      ),
              ),

              // 底部工具栏 - 两行布局，类似iPhone Safari
              Container(
                decoration: BoxDecoration(
                  color: themeColors.backgroundColor,
                  border: Border(
                    top: BorderSide(
                      color: themeColors.textColor.withValues(alpha: 0.2),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 第一行：水平滚动的地址栏
                    Container(
                      height: 48,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                      alignment: Alignment.center,
                      child: ListView.builder(
                        controller: _tabScrollController,
                        scrollDirection: Axis.horizontal,
                        itemCount: _tabs.length,
                        itemBuilder: (context, index) {
                          final tab = _tabs[index];
                          final isCurrentTab = index == _currentTabIndex;
                          final isEditing = _editingTabId == tab.id;
                          final controller = _addressControllers[tab.id];
                          final focusNode = _addressFocusNodes[tab.id];

                          if (controller == null || focusNode == null) {
                            return const SizedBox.shrink();
                          }

                          // 如果不在编辑状态，显示域名；编辑状态显示完整URL
                          if (!isEditing) {
                            final displayText = tab.url.isEmpty ? '' : _extractDomainFromUrl(tab.url);
                            // 只在内容真正改变时才更新controller
                            if (controller.text != displayText) {
                              controller.text = displayText;
                            }
                          }

                          return Container(
                            width: MediaQuery.of(context).size.width * 0.8,
                            margin: EdgeInsets.only(
                              left: index == 0 ? MediaQuery.of(context).size.width * 0.05 : 0, // 第一个tab左侧增加空白
                              right: index == _tabs.length - 1
                                  ? MediaQuery.of(context).size.width * 0.05 + 8
                                  : 8, // 最后一个tab右侧增加空白，其他tab保持8px间距
                            ),
                            decoration: BoxDecoration(
                              color: isCurrentTab
                                  ? themeColors.inputBoxBgColor
                                  : themeColors.inputBoxBgColor.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color:
                                    isCurrentTab ? themeColors.textColor : themeColors.textColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // 关闭按钮（仅在当前tab显示）
                                if (isCurrentTab)
                                  GestureDetector(
                                    onTap: () => _closeTab(index),
                                    child: Container(
                                      width: 28,
                                      height: 28,
                                      margin: const EdgeInsets.only(left: 8, right: 4),
                                      decoration: BoxDecoration(
                                        color: themeColors.textColor.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(14),
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        size: 18,
                                        color: themeColors.textColor.withValues(alpha: 0.7),
                                      ),
                                    ),
                                  ),
                                // 为非当前tab添加左侧间距以保持对齐
                                if (!isCurrentTab) const SizedBox(width: 12),
                                // 加载指示器
                                if (tab.isLoading)
                                  Container(
                                    width: 18,
                                    height: 18,
                                    margin: const EdgeInsets.only(right: 8),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: themeColors.textColor,
                                    ),
                                  ),
                                // 地址栏输入框
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      if (isCurrentTab) {
                                        // 如果是当前tab，直接聚焦到地址栏进行编辑
                                        focusNode.requestFocus();
                                      } else {
                                        // 如果不是当前tab，先切换到该tab
                                        _switchToTab(index);
                                      }
                                    },
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        textSelectionTheme: TextSelectionThemeData(
                                          cursorColor: themeColors.textColor,
                                          selectionColor: themeColors.textColor.withValues(alpha: 0.3),
                                          selectionHandleColor: themeColors.textColor,
                                        ),
                                      ),
                                      child: TextField(
                                        controller: controller,
                                        focusNode: focusNode,
                                        keyboardType: TextInputType.url,
                                        textInputAction: TextInputAction.go,
                                        cursorColor: themeColors.textColor,
                                        textAlign: isCurrentTab && isEditing ? TextAlign.start :  tab.url.isEmpty ? TextAlign.start : TextAlign.center,
                                        decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintText: (isEditing || tab.url.isEmpty) ? AppLocalizations.of(context)!.enterUrl : "",
                                          hintStyle: TextStyle(fontSize: 14, color: Colors.grey.shade400),
                                          contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 2),
                                          isDense: true,
                                        ),
                                        style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color: isCurrentTab
                                                ? themeColors.textColor
                                                : themeColors.textColor.withValues(alpha: 0.7)),
                                        onSubmitted: (value) => _loadUrlFromAddressBar(tab.id),
                                        textAlignVertical: TextAlignVertical.center,
                                        enabled: isCurrentTab, // 只有当前tab可以编辑
                                      ),
                                    ),
                                  ),
                                ),
                                // 搜索/停止/刷新按钮（仅在当前tab显示）
                                if (isCurrentTab)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4.0, right: 8.0),
                                    child: GestureDetector(
                                      onTap: () {
                                        if (tab.isLoading) {
                                          _stopLoading();
                                        } else if (_shouldShowSearchIcon(tab.id)) {
                                          _loadUrlFromAddressBar(tab.id);
                                        } else {
                                          // 刷新页面
                                          _refreshTab(tab.id);
                                        }
                                      },
                                      child: tab.isLoading
                                          ? Icon(Icons.close, size: 22, color: themeColors.textColor)
                                          : _shouldShowSearchIcon(tab.id)
                                              ? Image.asset(
                                                  ThemeManager.getImagePath('icon_search'),
                                                  width: 22,
                                                  height: 22,
                                                  color: themeColors.textColor,
                                                )
                                              : Icon(Icons.refresh, size: 22, color: themeColors.textColor),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    // 第二行：导航控制按钮
                    Container(
                      height: 50,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      child: Row(
                        children: [
                          // 左侧：主页、后退、前进和下载按钮
                          Row(
                            children: [
                              IconButton(
                                icon: SvgPicture.asset(
                                  ThemeManager.getImagePath('icon_home'),
                                  width: 24,
                                  height: 24,
                                ),
                                onPressed: _goHome,
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: Icon(Icons.arrow_back_ios,
                                    color: _canGoBackState
                                        ? themeColors.textColor
                                        : themeColors.textColor.withValues(alpha: 0.3),
                                    size: 24),
                                onPressed: _canGoBackState ? _goBack : null,
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: _canGoForwardState
                                        ? themeColors.textColor
                                        : themeColors.textColor.withValues(alpha: 0.3),
                                    size: 24),
                                onPressed: _canGoForwardState ? _goForward : null,
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              // 下载按钮 - 移到左侧导航控件区域
                              IconButton(
                                icon: _isDownloading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.black87,
                                        ),
                                      )
                                    : Image.asset(
                                        ThemeManager.getImagePath('icon_download'),
                                        width: 24,
                                        height: 24,
                                        color: _canTranslateCurrentTab() ? themeColors.textColor : themeColors.textColor.withValues(alpha: 0.3),
                                      ),
                                onPressed: _canTranslateCurrentTab() && !_isDownloading
                                    ? () => _startDownload(context)
                                    : null,
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),

                          // 中间：翻译滑块控件（使用Expanded来居中）
                          Expanded(
                            child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // 翻译滑块/开关控件
                                  Container(
                                    width: 120,
                                    height: 38,
                                    decoration: BoxDecoration(
                                      color: _translationToggleEnabled
                                        ? const Color(0xFF4CAF50).withValues(alpha: 0.2) // Light green background when enabled
                                        : Colors.grey[200],
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: _translationToggleEnabled
                                          ? const Color(0xFF4CAF50)
                                          : Colors.grey[400]!,
                                        width: 1,
                                      ),
                                    ),
                                    child: _isLocalTranslating
                                        ? const Center(
                                            child: SizedBox(
                                              width: 22,
                                              height: 22,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          )
                                        : GestureDetector(
                                            onTap: (_canTranslateCurrentTab() && _servicesInitialized)
                                                ? () => _toggleLocalTranslation(context)
                                                : () {
                                                  ToastWidget.show(
                                                    AppLocalizations.of(context)!.loading
                                                  );
                                                  },
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                // Translation icon
                                                _translationToggleEnabled
                                                  ? Icon(
                                                      Icons.translate,
                                                      size: 20,
                                                      color: const Color(0xFF4CAF50),
                                                    )
                                                  : SvgPicture.asset(
                                                      ThemeManager.getImagePath('icon_translate'),
                                                      width: 20,
                                                      height: 20,
                                                      colorFilter: ColorFilter.mode(
                                                        (_canTranslateCurrentTab() && _servicesInitialized) ? Colors.black87 : Colors.grey[600]!,
                                                        BlendMode.srcIn,
                                                      ),
                                                    ),
                                                const SizedBox(width: 6),
                                                // Toggle switch
                                                Container(
                                                  width: 40,
                                                  height: 20,
                                                  decoration: BoxDecoration(
                                                    color: _translationToggleEnabled
                                                      ? const Color(0xFF4CAF50)
                                                      : Colors.grey[400],
                                                    borderRadius: BorderRadius.circular(10),
                                                  ),
                                                  child: AnimatedAlign(
                                                    duration: const Duration(milliseconds: 200),
                                                    alignment: _translationToggleEnabled
                                                      ? Alignment.centerRight
                                                      : Alignment.centerLeft,
                                                    child: Container(
                                                      width: 16,
                                                      height: 16,
                                                      margin: const EdgeInsets.all(2),
                                                      decoration: const BoxDecoration(
                                                        color: Colors.white,
                                                        shape: BoxShape.circle,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // 右侧：更多菜单按钮
                          IconButton(
                            icon: Icon(
                              Icons.more_vert,
                              color: _isMoreMenuVisible ? const Color(0xFFCDEE2D) : themeColors.textColor,
                              size: 28
                            ),
                            onPressed: _showMoreMenu,
                            padding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
