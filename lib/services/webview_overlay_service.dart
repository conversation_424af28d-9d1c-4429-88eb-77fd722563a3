import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/services/local_ocr_service.dart';

/// Service for managing JavaScript-based text overlays in WebView
/// Handles injection, positioning, and cleanup of translated text overlays
class WebViewOverlayService {
  static final WebViewOverlayService _instance = WebViewOverlayService._internal();
  factory WebViewOverlayService() => _instance;
  WebViewOverlayService._internal();

  bool _isInitialized = false;
  bool _overlaysActive = false;
  InAppWebViewController? _currentController;
  final List<String> _activeOverlayIds = [];

  /// Initialize the overlay service
  Future<void> initialize() async {
    try {
      _isInitialized = true;
      debugPrint('WebViewOverlayService: Initialized successfully');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize - $e');
      throw OverlayException('Failed to initialize overlay service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if overlays are currently active
  bool get overlaysActive => _overlaysActive;

  /// Set the current WebView controller
  void setController(InAppWebViewController? controller) {
    _currentController = controller;
  }

  /// Inject CSS styles for overlays
  Future<void> _injectOverlayStyles() async {
    if (_currentController == null) return;

    const cssStyles = '''
      <style id="translation-overlay-styles">
        .translation-overlay {
          position: absolute;
          background-color: rgba(255, 255, 255, 0.9);
          border: 1px solid #CDEE2D;
          border-radius: 4px;
          padding: 2px 4px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 500;
          color: #333;
          z-index: 10000;
          pointer-events: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          transition: opacity 0.2s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 300px;
        }
        
        .translation-overlay.fade-in {
          opacity: 1;
        }
        
        .translation-overlay.fade-out {
          opacity: 0;
        }
        
        .translation-overlay-container {
          position: relative;
          z-index: 9999;
        }
      </style>
    ''';

    try {
      await _currentController!.evaluateJavascript(source: '''
        if (!document.getElementById('translation-overlay-styles')) {
          document.head.insertAdjacentHTML('beforeend', `$cssStyles`);
        }
      ''');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject styles - $e');
    }
  }

  /// Inject JavaScript functions for overlay management
  Future<void> _injectOverlayScripts() async {
    if (_currentController == null) return;

    const jsScript = '''
      window.translationOverlays = window.translationOverlays || {
        overlays: new Map(),
        scrollHandler: null,

        createImageOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
          // Find the image element
          const images = document.querySelectorAll('img');
          let targetImage = null;

          for (let img of images) {
            if (img.src === imageUrl || img.src.includes(imageUrl.split('/').pop())) {
              targetImage = img;
              break;
            }
          }

          if (!targetImage) {
            console.warn('Target image not found for overlay:', imageUrl);
            return null;
          }

          // Get image position and size
          const imageRect = targetImage.getBoundingClientRect();
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          // Calculate absolute position relative to image
          const absoluteX = imageRect.left + scrollX + (x * imageRect.width / targetImage.naturalWidth);
          const absoluteY = imageRect.top + scrollY + (y * imageRect.height / targetImage.naturalHeight);

          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = absoluteX + 'px';
          overlay.style.top = absoluteY + 'px';
          overlay.style.fontSize = fontSize + 'px';
          overlay.style.minWidth = Math.min(width * imageRect.width / targetImage.naturalWidth, 300) + 'px';
          overlay.style.maxWidth = '300px';

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            targetImage: targetImage,
            originalX: x,
            originalY: y,
            scrollX: scrollX,
            scrollY: scrollY
          });

          // Fade in animation
          setTimeout(() => overlay.classList.add('fade-in'), 10);

          return overlay;
        },

        createOverlay: function(id, text, x, y, width, height, fontSize) {
          // Fallback to absolute positioning
          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = x + 'px';
          overlay.style.top = y + 'px';
          overlay.style.fontSize = fontSize + 'px';
          overlay.style.minWidth = Math.min(width, 300) + 'px';
          overlay.style.maxWidth = '300px';

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            originalX: x,
            originalY: y,
            scrollX: window.scrollX,
            scrollY: window.scrollY
          });

          // Fade in animation
          setTimeout(() => overlay.classList.add('fade-in'), 10);

          return overlay;
        },
        
        removeOverlay: function(id) {
          const overlayData = this.overlays.get(id);
          if (overlayData) {
            overlayData.element.classList.add('fade-out');
            setTimeout(() => {
              if (overlayData.element.parentNode) {
                overlayData.element.parentNode.removeChild(overlayData.element);
              }
              this.overlays.delete(id);
            }, 200);
          }
        },
        
        removeAllOverlays: function() {
          this.overlays.forEach((overlayData, id) => {
            this.removeOverlay(id);
          });
          this.overlays.clear();
        },
        
        updateOverlayPositions: function() {
          const currentScrollX = window.scrollX;
          const currentScrollY = window.scrollY;

          this.overlays.forEach((overlayData, id) => {
            if (overlayData.targetImage) {
              // Image-relative positioning
              const imageRect = overlayData.targetImage.getBoundingClientRect();
              const absoluteX = imageRect.left + currentScrollX + (overlayData.originalX * imageRect.width / overlayData.targetImage.naturalWidth);
              const absoluteY = imageRect.top + currentScrollY + (overlayData.originalY * imageRect.height / overlayData.targetImage.naturalHeight);

              overlayData.element.style.left = absoluteX + 'px';
              overlayData.element.style.top = absoluteY + 'px';
            } else {
              // Fallback to absolute positioning
              const deltaX = currentScrollX - overlayData.scrollX;
              const deltaY = currentScrollY - overlayData.scrollY;

              overlayData.element.style.left = (overlayData.originalX - deltaX) + 'px';
              overlayData.element.style.top = (overlayData.originalY - deltaY) + 'px';
            }
          });
        },
        
        setupScrollHandler: function() {
          if (this.scrollHandler) return;
          
          this.scrollHandler = () => {
            this.updateOverlayPositions();
          };
          
          window.addEventListener('scroll', this.scrollHandler, { passive: true });
          window.addEventListener('resize', this.scrollHandler, { passive: true });
        },
        
        removeScrollHandler: function() {
          if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
            window.removeEventListener('resize', this.scrollHandler);
            this.scrollHandler = null;
          }
        }
      };
    ''';

    try {
      await _currentController!.evaluateJavascript(source: jsScript);
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject scripts - $e');
    }
  }

  /// Create and display overlays for translated text elements
  Future<void> showOverlays(List<OcrTextElement> translatedElements, List<String> imageUrls) async {
    if (!_isInitialized || _currentController == null) {
      throw OverlayException('Overlay service not initialized or controller not set');
    }

    try {
      // Inject styles and scripts if not already done
      await _injectOverlayStyles();
      await _injectOverlayScripts();

      // Setup scroll handler
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.setupScrollHandler();'
      );

      // Group elements by image (assuming elements are ordered by image)
      int elementsPerImage = translatedElements.length ~/ imageUrls.length;
      if (elementsPerImage == 0) elementsPerImage = 1;

      // Create overlays for each translated element
      for (int i = 0; i < translatedElements.length; i++) {
        final element = translatedElements[i];
        final overlayId = 'overlay_$i';
        final imageIndex = i ~/ elementsPerImage;
        final imageUrl = imageIndex < imageUrls.length ? imageUrls[imageIndex] : imageUrls.last;

        final boundingBox = element.boundingBox;
        final fontSize = _calculateFontSize(boundingBox.width, boundingBox.height);

        // Create overlay positioned relative to the specific image
        await _currentController!.evaluateJavascript(source: '''
          window.translationOverlays.createImageOverlay(
            '$overlayId',
            ${_escapeJavaScriptString(element.text)},
            '$imageUrl',
            ${boundingBox.left},
            ${boundingBox.top},
            ${boundingBox.width},
            ${boundingBox.height},
            $fontSize
          );
        ''');

        _activeOverlayIds.add(overlayId);
      }

      _overlaysActive = true;
      debugPrint('WebViewOverlayService: Created ${translatedElements.length} overlays for ${imageUrls.length} images');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show overlays - $e');
      throw OverlayException('Failed to show overlays: $e');
    }
  }

  /// Hide all overlays
  Future<void> hideOverlays() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeAllOverlays();'
      );
      
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeScrollHandler();'
      );

      _activeOverlayIds.clear();
      _overlaysActive = false;
      debugPrint('WebViewOverlayService: Hidden all overlays');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide overlays - $e');
    }
  }

  /// Clean up all overlays and handlers
  Future<void> cleanup() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await hideOverlays();
      
      // Remove injected styles
      await _currentController!.evaluateJavascript(source: '''
        const styleElement = document.getElementById('translation-overlay-styles');
        if (styleElement) {
          styleElement.parentNode.removeChild(styleElement);
        }
      ''');
      
      debugPrint('WebViewOverlayService: Cleanup completed');
    } catch (e) {
      debugPrint('WebViewOverlayService: Cleanup failed - $e');
    }
  }

  /// Calculate appropriate font size based on bounding box dimensions
  double _calculateFontSize(double width, double height) {
    // Base font size calculation based on bounding box height
    double fontSize = height * 0.7; // 70% of bounding box height
    
    // Clamp font size to reasonable bounds
    fontSize = fontSize.clamp(10.0, 18.0);
    
    return fontSize;
  }

  /// Escape JavaScript string to prevent injection attacks
  String _escapeJavaScriptString(String text) {
    return "'" + text
        .replaceAll('\\', '\\\\')
        .replaceAll("'", "\\'")
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t') + "'";
  }

  /// Dispose the service
  void dispose() {
    _currentController = null;
    _activeOverlayIds.clear();
    _overlaysActive = false;
    _isInitialized = false;
    debugPrint('WebViewOverlayService: Disposed');
  }
}

/// Custom exception for overlay operations
class OverlayException implements Exception {
  final String message;
  final dynamic originalError;

  const OverlayException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OverlayException: $message (Original error: $originalError)';
    }
    return 'OverlayException: $message';
  }
}
