import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:vision_text_recognition/vision_text_recognition.dart';

/// Local OCR service using vision_text_recognition package
/// Provides text recognition from images with comprehensive error handling
class LocalOcrService {
  static final LocalOcrService _instance = LocalOcrService._internal();
  factory LocalOcrService() => _instance;
  LocalOcrService._internal();

  late final VisionTextRecognition _visionTextRecognition;
  bool _isInitialized = false;

  /// Initialize the OCR service
  /// Must be called before using any OCR functionality
  Future<void> initialize() async {
    try {
      _visionTextRecognition = VisionTextRecognition();
      _isInitialized = true;
      debugPrint('LocalOcrService: Initialized successfully');
    } catch (e) {
      debugPrint('LocalOcrService: Failed to initialize - $e');
      throw OcrException('Failed to initialize OCR service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Extract text from image bytes
  /// Returns a list of recognized text elements with their positions
  Future<List<OcrTextElement>> extractTextFromBytes(Uint8List imageBytes) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from image bytes');
      
      // Convert bytes to VisionImage
      final visionImage = VisionImage.fromBytes(imageBytes);
      
      // Perform text recognition
      final recognizedText = await _visionTextRecognition.recognize(visionImage);
      
      if (recognizedText == null) {
        debugPrint('LocalOcrService: No text recognized in image');
        return [];
      }

      // Convert to our format
      final textElements = <OcrTextElement>[];
      
      for (final block in recognizedText.blocks) {
        for (final line in block.lines) {
          for (final element in line.elements) {
            final boundingBox = element.boundingBox;
            if (boundingBox != null) {
              textElements.add(OcrTextElement(
                text: element.text,
                boundingBox: OcrBoundingBox(
                  left: boundingBox.left.toDouble(),
                  top: boundingBox.top.toDouble(),
                  right: boundingBox.right.toDouble(),
                  bottom: boundingBox.bottom.toDouble(),
                ),
                confidence: element.confidence ?? 0.0,
              ));
            }
          }
        }
      }

      debugPrint('LocalOcrService: Extracted ${textElements.length} text elements');
      return textElements;
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Extract text from image file path
  Future<List<OcrTextElement>> extractTextFromPath(String imagePath) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from path: $imagePath');
      
      // Create VisionImage from file path
      final visionImage = VisionImage.fromPath(imagePath);
      
      // Perform text recognition
      final recognizedText = await _visionTextRecognition.recognize(visionImage);
      
      if (recognizedText == null) {
        debugPrint('LocalOcrService: No text recognized in image');
        return [];
      }

      // Convert to our format
      final textElements = <OcrTextElement>[];
      
      for (final block in recognizedText.blocks) {
        for (final line in block.lines) {
          for (final element in line.elements) {
            final boundingBox = element.boundingBox;
            if (boundingBox != null) {
              textElements.add(OcrTextElement(
                text: element.text,
                boundingBox: OcrBoundingBox(
                  left: boundingBox.left.toDouble(),
                  top: boundingBox.top.toDouble(),
                  right: boundingBox.right.toDouble(),
                  bottom: boundingBox.bottom.toDouble(),
                ),
                confidence: element.confidence ?? 0.0,
              ));
            }
          }
        }
      }

      debugPrint('LocalOcrService: Extracted ${textElements.length} text elements');
      return textElements;
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    debugPrint('LocalOcrService: Disposed');
  }
}

/// Represents a text element recognized by OCR
class OcrTextElement {
  final String text;
  final OcrBoundingBox boundingBox;
  final double confidence;

  const OcrTextElement({
    required this.text,
    required this.boundingBox,
    required this.confidence,
  });

  @override
  String toString() {
    return 'OcrTextElement(text: "$text", boundingBox: $boundingBox, confidence: $confidence)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'boundingBox': boundingBox.toJson(),
      'confidence': confidence,
    };
  }

  /// Create from JSON
  factory OcrTextElement.fromJson(Map<String, dynamic> json) {
    return OcrTextElement(
      text: json['text'] as String,
      boundingBox: OcrBoundingBox.fromJson(json['boundingBox'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
    );
  }
}

/// Represents a bounding box for OCR text
class OcrBoundingBox {
  final double left;
  final double top;
  final double right;
  final double bottom;

  const OcrBoundingBox({
    required this.left,
    required this.top,
    required this.right,
    required this.bottom,
  });

  /// Get width of the bounding box
  double get width => right - left;

  /// Get height of the bounding box
  double get height => bottom - top;

  /// Get center point of the bounding box
  Offset get center => Offset(left + width / 2, top + height / 2);

  /// Convert to Rect
  Rect toRect() => Rect.fromLTRB(left, top, right, bottom);

  @override
  String toString() {
    return 'OcrBoundingBox(left: $left, top: $top, right: $right, bottom: $bottom)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'left': left,
      'top': top,
      'right': right,
      'bottom': bottom,
    };
  }

  /// Create from JSON
  factory OcrBoundingBox.fromJson(Map<String, dynamic> json) {
    return OcrBoundingBox(
      left: (json['left'] as num).toDouble(),
      top: (json['top'] as num).toDouble(),
      right: (json['right'] as num).toDouble(),
      bottom: (json['bottom'] as num).toDouble(),
    );
  }
}

/// Custom exception for OCR operations
class OcrException implements Exception {
  final String message;
  final dynamic originalError;

  const OcrException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OcrException: $message (Original error: $originalError)';
    }
    return 'OcrException: $message';
  }
}
